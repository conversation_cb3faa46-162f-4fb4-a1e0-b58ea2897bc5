// ====================
// 丧尸模块
// ====================
(function() {
    // 更新丧尸
    function updateZombies() {
        for (const zombie of GameState.zombies) {
            // 保存当前位置
            const oldX = zombie.x;
            const oldY = zombie.y;
            
            // 追踪玩家
            const dx = GameState.player.x - zombie.x;
            const dy = GameState.player.y - zombie.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance > 0) {
                const speed = zombie.speed;
                zombie.x += (dx / distance) * speed;
                zombie.y += (dy / distance) * speed;
            }
            
            // 检查与墙壁的碰撞
            if (CollisionSystem.checkWallCollision(zombie.x, zombie.y, zombie.width, zombie.height)) {
                // 恢复位置
                zombie.x = oldX;
                zombie.y = oldY;
            }
            
            // 朝向角度
            zombie.angle = Math.atan2(dy, dx);
            
            // 移动粒子
            if (GameState.gameTime % 20 === 0) {
                const particleColor = zombie.type === 'BOOMER' ? 
                    'rgba(255, 100, 0, alpha)' : 'rgba(100, 150, 50, alpha)';
                ParticleSystem.createParticles(zombie.x, zombie.y + 15, 1, particleColor, 1);
            }
            
            // 自爆丧尸特殊逻辑
            if (zombie.type === 'BOOMER' && zombie.health <= 0) {
                // 创建爆炸效果
                ParticleSystem.createParticles(zombie.x, zombie.y, 30, 'rgba(255, 100, 0, alpha)', 3);
                
                // 检查是否对玩家造成伤害
                const playerDistance = Math.sqrt(
                    Math.pow(zombie.x - GameState.player.x, 2) +
                    Math.pow(zombie.y - GameState.player.y, 2)
                );
                
                if (playerDistance < 100) {
                    GameState.player.health -= 20;
                    UISystem.updateHealthUI();
                    AudioSystem.play('hurt');
                    
                    // 伤害粒子
                    ParticleSystem.createParticles(GameState.player.x, GameState.player.y, 5, 'rgba(255, 0, 0, alpha)', 2);
                }
            }
        }
    }

    // 导出丧尸模块接口
    window.Zombie = {
        update: updateZombies
    };
})();