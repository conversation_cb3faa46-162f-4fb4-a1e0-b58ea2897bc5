// ====================
// 门系统模块
// ====================
(function() {
    // 互动（开关门）
    function interactWithDoor() {
        // 找到最近的门
        let nearestDoor = null;
        let minDistance = Infinity;
        
        for (const door of GameState.doors) {
            const distance = Math.sqrt(
                Math.pow(door.x + door.width/2 - GameState.player.x, 2) +
                Math.pow(door.y + door.height/2 - GameState.player.y, 2)
            );
            
            if (distance < minDistance && distance < CONFIG.DOOR_INTERACT_DISTANCE) {
                minDistance = distance;
                nearestDoor = door;
            }
        }
        
        // 如果找到了门，开关它
        if (nearestDoor) {
            nearestDoor.open = !nearestDoor.open;
            AudioSystem.play('door');
        }
    }

    // 导出门系统接口
    window.DoorSystem = {
        interactWithDoor: interactWithDoor
    };
})();