// ====================
// 粒子系统模块
// ====================
(function() {
    class Particle {
        constructor(x, y, vx, vy, life, color, size = 2) {
            this.x = x;
            this.y = y;
            this.vx = vx;
            this.vy = vy;
            this.life = life;
            this.maxLife = life;
            this.color = color;
            this.size = size;
        }
        
        update() {
            this.x += this.vx;
            this.y += this.vy;
            this.life--;
            this.vy += 0.1; // 重力效果
        }
        
        draw(ctx) {
            const alpha = this.life / this.maxLife;
            ctx.fillStyle = this.color.replace('alpha', alpha);
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    // 创建粒子效果
    function createParticles(x, y, count, color, size = 2) {
        // 限制粒子数量
        if (GameState.particles.length > CONFIG.MAX_PARTICLES) return;
        
        for (let i = 0; i < count; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Math.random() * 3 + 1;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const life = Math.floor(Math.random() * 30 + 20);
            
            GameState.particles.push(new Particle(x, y, vx, vy, life, color, size));
        }
    }

    // 更新粒子
    function updateParticles() {
        // 限制粒子数量
        if (GameState.particles.length > CONFIG.MAX_PARTICLES) {
            GameState.particles.splice(CONFIG.MAX_PARTICLES);
        }
        
        for (let i = GameState.particles.length - 1; i >= 0; i--) {
            const particle = GameState.particles[i];
            particle.update();
            
            if (particle.life <= 0) {
                GameState.particles.splice(i, 1);
            }
        }
    }

    // 导出粒子系统接口
    window.ParticleSystem = {
        Particle: Particle,
        createParticles: createParticles,
        updateParticles: updateParticles
    };
})();