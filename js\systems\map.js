// ====================
// 地图系统模块
// ====================
(function() {
    // ====================
    // 房间类
    // ====================
    class Room {
        constructor(x, y, width, height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.centerX = Math.floor(x + width / 2);
            this.centerY = Math.floor(y + height / 2);
        }
        
        // 检查是否与另一个房间重叠
        intersects(other) {
            return (
                this.x < other.x + other.width &&
                this.x + this.width > other.x &&
                this.y < other.y + other.height &&
                this.y + this.height > other.y
            );
        }
        
        // 获取房间的边缘坐标
        getEdges() {
            return {
                left: this.x,
                right: this.x + this.width,
                top: this.y,
                bottom: this.y + this.height
            };
        }
        
        // 检查点是否在房间内
        containsPoint(x, y) {
            return (
                x >= this.x && 
                x < this.x + this.width && 
                y >= this.y && 
                y < this.y + this.height
            );
        }
    }

    // 生成平衡性优化的地图
    function generateBalancedMap() {
        // 初始化地图
        const map = [];
        for (let y = 0; y < CONFIG.MAP_HEIGHT; y++) {
            map[y] = [];
            for (let x = 0; x < CONFIG.MAP_WIDTH; x++) {
                // 默认设置为墙壁
                map[y][x] = TILE_TYPES.WALL;
            }
        }
        
        // 生成房间 - 增大房间尺寸
        const rooms = [];
        const maxRooms = 6 + Math.floor(Math.random() * 4); // 6-9个房间
        const minRoomSize = 10;  // 增大最小房间尺寸
        const maxRoomSize = 20; // 增大最大房间尺寸
        
        for (let i = 0; i < maxRooms; i++) {
            // 随机房间大小
            const width = Math.floor(Math.random() * (maxRoomSize - minRoomSize)) + minRoomSize;
            const height = Math.floor(Math.random() * (maxRoomSize - minRoomSize)) + minRoomSize;
            
            // 随机位置
            const x = Math.floor(Math.random() * (CONFIG.MAP_WIDTH - width - 4)) + 2;
            const y = Math.floor(Math.random() * (CONFIG.MAP_HEIGHT - height - 4)) + 2;
            
            const newRoom = new Room(x, y, width, height);
            
            // 检查是否与现有房间重叠
            let overlaps = false;
            for (const room of rooms) {
                if (newRoom.intersects(room)) {
                    overlaps = true;
                    break;
                }
            }
            
            // 如果不重叠，添加房间
            if (!overlaps) {
                rooms.push(newRoom);
                
                // 在地图上标记房间区域为空地
                for (let ry = y; ry < y + height; ry++) {
                    for (let rx = x; rx < x + width; rx++) {
                        map[ry][rx] = TILE_TYPES.EMPTY;
                    }
                }
            }
        }
        
        // 连接房间
        if (rooms.length > 1) {
            // 从第一个房间开始连接
            for (let i = 1; i < rooms.length; i++) {
                const prevRoom = rooms[i - 1];
                const currentRoom = rooms[i];
                
                // 创建L型走廊连接两个房间
                // 先水平连接，再垂直连接
                const prevX = prevRoom.centerX;
                const prevY = prevRoom.centerY;
                const currentX = currentRoom.centerX;
                const currentY = currentRoom.centerY;
                
                // 水平隧道 (增加宽度到3格)
                const minX = Math.min(prevX, currentX);
                const maxX = Math.max(prevX, currentX);
                for (let x = minX; x <= maxX; x++) {
                    if (prevY >= 2 && prevY < CONFIG.MAP_HEIGHT - 2 && 
                        x >= 2 && x < CONFIG.MAP_WIDTH - 2) {
                        // 创建3格宽的走廊
                        map[prevY][x] = TILE_TYPES.EMPTY;
                        map[prevY-1][x] = TILE_TYPES.EMPTY;
                        map[prevY+1][x] = TILE_TYPES.EMPTY;
                    }
                }
                
                // 垂直隧道 (增加宽度到3格)
                const minY = Math.min(prevY, currentY);
                const maxY = Math.max(prevY, currentY);
                for (let y = minY; y <= maxY; y++) {
                    if (y >= 2 && y < CONFIG.MAP_HEIGHT - 2 && 
                        currentX >= 2 && currentX < CONFIG.MAP_WIDTH - 2) {
                        // 创建3格宽的走廊
                        map[y][currentX] = TILE_TYPES.EMPTY;
                        map[y][currentX-1] = TILE_TYPES.EMPTY;
                        map[y][currentX+1] = TILE_TYPES.EMPTY;
                    }
                }
            }
        }
        
        // 在房间入口创建门 - 重新设计的门系统
        const doorPositions = [];

        for (let i = 0; i < rooms.length; i++) {
            const room = rooms[i];

            // 检查房间的四个边，寻找与走廊连接的入口
            // 上边
            for (let x = room.x; x < room.x + room.width; x++) {
                if (room.y > 0 && map[room.y - 1][x] === TILE_TYPES.EMPTY) {
                    // 找到连续的3格入口，在中间放置门
                    let entranceStart = x;
                    let entranceWidth = 0;
                    while (x < room.x + room.width && map[room.y - 1][x] === TILE_TYPES.EMPTY) {
                        entranceWidth++;
                        x++;
                    }
                    x--; // 回退一格

                    if (entranceWidth >= 3) {
                        const doorX = entranceStart + Math.floor(entranceWidth / 2);
                        doorPositions.push({
                            x: doorX,
                            y: room.y - 1,
                            type: 'ROOM_ENTRANCE',
                            orientation: 'horizontal',
                            roomId: i
                        });
                    }
                }
            }

            // 下边
            for (let x = room.x; x < room.x + room.width; x++) {
                if (room.y + room.height < CONFIG.MAP_HEIGHT && map[room.y + room.height][x] === TILE_TYPES.EMPTY) {
                    let entranceStart = x;
                    let entranceWidth = 0;
                    while (x < room.x + room.width && map[room.y + room.height][x] === TILE_TYPES.EMPTY) {
                        entranceWidth++;
                        x++;
                    }
                    x--;

                    if (entranceWidth >= 3) {
                        const doorX = entranceStart + Math.floor(entranceWidth / 2);
                        doorPositions.push({
                            x: doorX,
                            y: room.y + room.height,
                            type: 'ROOM_ENTRANCE',
                            orientation: 'horizontal',
                            roomId: i
                        });
                    }
                }
            }

            // 左边
            for (let y = room.y; y < room.y + room.height; y++) {
                if (room.x > 0 && map[y][room.x - 1] === TILE_TYPES.EMPTY) {
                    let entranceStart = y;
                    let entranceHeight = 0;
                    while (y < room.y + room.height && map[y][room.x - 1] === TILE_TYPES.EMPTY) {
                        entranceHeight++;
                        y++;
                    }
                    y--;

                    if (entranceHeight >= 3) {
                        const doorY = entranceStart + Math.floor(entranceHeight / 2);
                        doorPositions.push({
                            x: room.x - 1,
                            y: doorY,
                            type: 'ROOM_ENTRANCE',
                            orientation: 'vertical',
                            roomId: i
                        });
                    }
                }
            }

            // 右边
            for (let y = room.y; y < room.y + room.height; y++) {
                if (room.x + room.width < CONFIG.MAP_WIDTH && map[y][room.x + room.width] === TILE_TYPES.EMPTY) {
                    let entranceStart = y;
                    let entranceHeight = 0;
                    while (y < room.y + room.height && map[y][room.x + room.width] === TILE_TYPES.EMPTY) {
                        entranceHeight++;
                        y++;
                    }
                    y--;

                    if (entranceHeight >= 3) {
                        const doorY = entranceStart + Math.floor(entranceHeight / 2);
                        doorPositions.push({
                            x: room.x + room.width,
                            y: doorY,
                            type: 'ROOM_ENTRANCE',
                            orientation: 'vertical',
                            roomId: i
                        });
                    }
                }
            }
        }

        // 在地图上标记门的位置
        for (const door of doorPositions) {
            if (door.orientation === 'horizontal') {
                // 水平门占据3格宽度
                for (let i = -1; i <= 1; i++) {
                    if (door.x + i >= 0 && door.x + i < CONFIG.MAP_WIDTH) {
                        map[door.y][door.x + i] = TILE_TYPES.DOOR_HORIZONTAL;
                    }
                }
            } else {
                // 垂直门占据3格高度
                for (let i = -1; i <= 1; i++) {
                    if (door.y + i >= 0 && door.y + i < CONFIG.MAP_HEIGHT) {
                        map[door.y + i][door.x] = TILE_TYPES.DOOR_VERTICAL;
                    }
                }
            }
        }
        
        return { map, rooms, doorPositions };
    }

    // 根据地图生成墙壁和门
    function createWallsAndDoorsFromMap(mapData) {
        const { map, rooms, doorPositions } = mapData;
        GameState.map = map;
        GameState.rooms = rooms;
        GameState.walls = [];
        GameState.doors = [];

        // 确定起始房间（第一个房间）
        if (rooms.length > 0) {
            GameState.startRoom = rooms[0];
        }

        // 创建墙壁
        for (let y = 0; y < CONFIG.MAP_HEIGHT; y++) {
            for (let x = 0; x < CONFIG.MAP_WIDTH; x++) {
                const tileX = x * CONFIG.TILE_SIZE;
                const tileY = y * CONFIG.TILE_SIZE;

                if (map[y][x] === TILE_TYPES.WALL) {
                    GameState.walls.push({
                        x: tileX,
                        y: tileY,
                        width: CONFIG.TILE_SIZE,
                        height: CONFIG.TILE_SIZE
                    });
                }
            }
        }

        // 创建新的门系统
        if (doorPositions) {
            for (const doorPos of doorPositions) {
                const doorConfig = CONFIG.DOOR_TYPES[doorPos.type];
                const centerX = doorPos.x * CONFIG.TILE_SIZE + CONFIG.TILE_SIZE / 2;
                const centerY = doorPos.y * CONFIG.TILE_SIZE + CONFIG.TILE_SIZE / 2;

                let door;
                if (doorPos.orientation === 'horizontal') {
                    // 水平门（横跨走廊）
                    door = {
                        x: centerX - doorConfig.width / 2,
                        y: centerY - doorConfig.height / 2,
                        width: doorConfig.width,
                        height: doorConfig.height,
                        centerX: centerX,
                        centerY: centerY,
                        type: doorPos.type,
                        orientation: 'horizontal',
                        open: Math.random() > 0.7, // 30%概率初始开启
                        openProgress: Math.random() > 0.7 ? 1.0 : 0.0, // 开门进度
                        roomId: doorPos.roomId,
                        config: doorConfig
                    };
                } else {
                    // 垂直门（横跨走廊）
                    door = {
                        x: centerX - doorConfig.height / 2, // 注意：垂直门的宽高互换
                        y: centerY - doorConfig.width / 2,
                        width: doorConfig.height,
                        height: doorConfig.width,
                        centerX: centerX,
                        centerY: centerY,
                        type: doorPos.type,
                        orientation: 'vertical',
                        open: Math.random() > 0.7,
                        openProgress: Math.random() > 0.7 ? 1.0 : 0.0,
                        roomId: doorPos.roomId,
                        config: doorConfig
                    };
                }

                GameState.doors.push(door);
            }
        }
    }

    // 导出地图系统接口
    window.MapSystem = {
        Room: Room,
        generateBalancedMap: generateBalancedMap,
        createWallsAndDoorsFromMap: createWallsAndDoorsFromMap
    };
})();