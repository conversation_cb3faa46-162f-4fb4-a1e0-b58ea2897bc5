// ====================
// 子弹模块
// ====================
(function() {
    // 射击
    function shoot() {
        const weaponConfig = CONFIG.WEAPONS[GameState.weapon.type];
        const now = Date.now();
        
        if (now - GameState.mouse.lastShot > weaponConfig.fireRate && 
            GameState.weapon.ammo > 0 && 
            !GameState.weapon.isReloading &&
            GameState.bullets.length < CONFIG.MAX_BULLETS) {
            
            GameState.mouse.lastShot = now;
            GameState.weapon.ammo--;
            AudioSystem.play('shoot');
            UISystem.updateAmmoUI();
            
            // 创建子弹
            const bulletSpeed = CONFIG.BULLET_SPEED;
            const angle = GameState.player.angle;
            const bullet = {
                x: GameState.player.x,
                y: GameState.player.y,
                vx: Math.cos(angle) * bulletSpeed,
                vy: Math.sin(angle) * bulletSpeed,
                width: 5,
                height: 5,
                life: 100,
                damage: weaponConfig.damage
            };
            
            GameState.bullets.push(bullet);
            
            // 创建枪口火焰粒子
            ParticleSystem.createParticles(
                GameState.player.x + Math.cos(angle) * 35,
                GameState.player.y + Math.sin(angle) * 35,
                3, 'rgba(255, 200, 0, alpha)', 2
            );
        }
    }

    // 更新子弹
    function updateBullets() {
        for (let i = GameState.bullets.length - 1; i >= 0; i--) {
            const bullet = GameState.bullets[i];
            
            // 保存当前位置
            const oldX = bullet.x;
            const oldY = bullet.y;
            
            // 更新位置
            bullet.x += bullet.vx;
            bullet.y += bullet.vy;
            bullet.life--;
            
            // 创建尾迹粒子
            if (GameState.gameTime % 3 === 0) {
                ParticleSystem.createParticles(bullet.x, bullet.y, 1, 'rgba(255, 255, 0, 0.7)', 1);
            }
            
            // 检查与墙壁的碰撞
            const bulletRect = { x: bullet.x - 2.5, y: bullet.y - 2.5, width: 5, height: 5 };
            let hitWall = false;
            
            for (const wall of GameState.walls) {
                if (CollisionSystem.checkRectCollision(bulletRect, wall)) {
                    hitWall = true;
                    break;
                }
            }
            
            for (const door of GameState.doors) {
                if (!door.open && CollisionSystem.checkRectCollision(bulletRect, door)) {
                    hitWall = true;
                    break;
                }
            }
            
            // 边界检测或撞击墙壁
            if (bullet.x < 0 || bullet.x > 1200 || bullet.y < 0 || bullet.y > 800 || bullet.life <= 0 || hitWall) {
                ParticleSystem.createParticles(bullet.x, bullet.y, 2, 'rgba(255, 255, 0, alpha)', 1);
                GameState.bullets.splice(i, 1);
                continue;
            }
            
            // 碰撞检测（与丧尸）
            for (let j = GameState.zombies.length - 1; j >= 0; j--) {
                const zombie = GameState.zombies[j];
                
                // 简化碰撞检测
                const collision = (
                    bullet.x > zombie.x - zombie.width/2 &&
                    bullet.x < zombie.x + zombie.width/2 &&
                    bullet.y > zombie.y - zombie.height/2 &&
                    bullet.y < zombie.y + zombie.height/2
                );
                
                if (collision) {
                    // 造成伤害
                    zombie.health -= bullet.damage;
                    
                    // 击中粒子
                    ParticleSystem.createParticles(bullet.x, bullet.y, 5, 'rgba(255, 0, 0, alpha)', 1);
                    
                    // 检查是否死亡
                    if (zombie.health <= 0) {
                        GameState.zombies.splice(j, 1);
                        GameState.zombiesKilled++;
                        GameState.score += zombie.type === 'FAST' ? 20 : 
                                          zombie.type === 'TANK' ? 50 : 
                                          zombie.type === 'BOOMER' ? 40 : 10;
                        
                        // 死亡粒子
                        ParticleSystem.createParticles(zombie.x, zombie.y, 10, 'rgba(100, 150, 50, alpha)', 2);
                        
                        // 随机掉落物品
                        if (Math.random() < CONFIG.ZOMBIE_TYPES[zombie.type].dropRate) {
                            ItemSystem.spawnItem(zombie.x, zombie.y);
                        }
                        
                        // 检查关卡完成（放在循环外）
                        setTimeout(() => {
                            LevelManager.checkLevelComplete();
                        }, 0);
                    }
                    
                    // 移除子弹
                    GameState.bullets.splice(i, 1);
                    break;
                }
            }
        }
    }

    // 导出子弹模块接口
    window.Bullet = {
        shoot: shoot,
        update: updateBullets
    };
})();