// ====================
// 宝箱系统模块
// ====================
(function() {
    // 创建宝箱
    function createChest(x, y, rarity) {
        const chestConfig = CONFIG.CHEST[rarity.toUpperCase()] || CONFIG.CHEST.COMMON;
        
        GameState.chests.push({
            x: x,
            y: y,
            width: 30,
            height: 20,
            rarity: rarity,
            name: chestConfig.name,
            icon: chestConfig.icon,
            isOpened: false,
            dropRate: chestConfig.dropRate
        });
    }
    
    // 生成随机宝箱
    function spawnRandomChest(x, y) {
        const rand = Math.random();
        let rarity;
        
        if (rand < 0.1) {
            rarity = "legendary";
        } else if (rand < 0.4) {
            rarity = "rare";
        } else {
            rarity = "common";
        }
        
        createChest(x, y, rarity);
    }
    
    // 打开宝箱
    function openChest(chestIndex) {
        const chest = GameState.chests[chestIndex];
        if (chest.isOpened) return;
        
        chest.isOpened = true;
        AudioSystem.play('chest');
        
        // 根据稀有度生成奖励
        let reward;
        const rand = Math.random();
        
        // 稀有度越高，获得好物品的概率越大
        if (chest.rarity === "legendary") {
            // 70%概率获得武器，30%概率获得高级物品
            if (rand < 0.7) {
                // 随机武器
                const weaponTypes = Object.keys(CONFIG.WEAPONS);
                const randomType = weaponTypes[Math.floor(Math.random() * weaponTypes.length)];
                const weaponConfig = CONFIG.WEAPONS[randomType];
                
                reward = {
                    type: "weapon",
                    name: weaponConfig.name,
                    icon: "🔫",
                    weaponType: randomType
                };
            } else {
                // 高级物品（护盾/速度提升）
                const itemTypes = ["shield", "speed"];
                const randomEffect = itemTypes[Math.floor(Math.random() * itemTypes.length)];
                
                if (randomEffect === "shield") {
                    reward = {
                        type: "item",
                        name: "超级护盾",
                        icon: "🛡",
                        effect: "shield",
                        value: 70
                    };
                } else {
                    reward = {
                        type: "item",
                        name: "超级速度提升",
                        icon: "⏩",
                        effect: "speed",
                        value: 3
                    };
                }
            }
        } else if (chest.rarity === "rare") {
            // 50%概率获得物品，50%概率获得弹药
            if (rand < 0.5) {
                reward = {
                    type: "item",
                    name: "大容量弹药包",
                    icon: "♦",
                    effect: "ammo",
                    value: 50
                };
            } else {
                reward = {
                    type: "item",
                    name: "超级速度提升",
                    icon: "⏩",
                    effect: "speed",
                    value: 2
                };
            }
        } else {
            // 普通宝箱：80%医疗包，20%弹药
            if (rand < 0.8) {
                reward = {
                    type: "item",
                    name: "医疗包",
                    icon: "✚",
                    effect: "heal",
                    value: 30
                };
            } else {
                reward = {
                    type: "item",
                    name: "弹药包",
                    icon: "♦",
                    effect: "ammo",
                    value: 20
                };
            }
        }
        
        // 应用奖励
        if (reward.type === "weapon") {
            // 更换武器
            GameState.weapon.type = reward.weaponType;
            const weaponConfig = CONFIG.WEAPONS[reward.weaponType];
            GameState.weapon.ammo = weaponConfig.ammo;
            GameState.weapon.maxAmmo = weaponConfig.ammo;
            UISystem.updateAmmoUI();
        } else {
            // 添加到背包
            let emptySlot = -1;
            for (let i = 0; i < GameState.inventory.length; i++) {
                if (GameState.inventory[i] === null) {
                    emptySlot = i;
                    break;
                }
            }
            
            if (emptySlot !== -1) {
                GameState.inventory[emptySlot] = {
                    type: reward.type,
                    name: reward.name,
                    icon: reward.icon,
                    effect: reward.effect,
                    value: reward.value
                };
                ItemSystem.updateInventoryUI();
            } else {
                // 背包已满，在屏幕显示提示
                showTemporaryMessage("背包已满！");
            }
        }
        
        // 移除已打开的宝箱
        setTimeout(() => {
            GameState.chests.splice(chestIndex, 1);
        }, 1000);
    }
    
    // 显示临时消息
    function showTemporaryMessage(message) {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = 'temporary-message';
        messageElement.textContent = message;
        messageElement.style.position = 'absolute';
        messageElement.style.top = '50%';
        messageElement.style.left = '50%';
        messageElement.style.transform = 'translate(-50%, -50%)';
        messageElement.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        messageElement.style.color = 'white';
        messageElement.style.padding = '10px 20px';
        messageElement.style.borderRadius = '5px';
        messageElement.style.zIndex = '100';
        messageElement.style.fontSize = '20px';
        
        document.getElementById('ui').appendChild(messageElement);
        
        // 2秒后移除消息
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, 2000);
    }
    
    // 检查玩家是否靠近宝箱
    function checkPlayerChestProximity() {
        for (let i = 0; i < GameState.chests.length; i++) {
            const chest = GameState.chests[i];
            if (chest.isOpened) continue;
            
            const distance = Math.sqrt(
                Math.pow(chest.x - GameState.player.x, 2) +
                Math.pow(chest.y - GameState.player.y, 2)
            );
            
            // 如果玩家靠近宝箱，可以按E打开
            if (distance < CONFIG.DOOR_INTERACT_DISTANCE) {
                return i; // 返回宝箱索引
            }
        }
        
        return -1; // 没有靠近任何宝箱
    }
    
    // 导出宝箱系统接口
    window.ChestSystem = {
        createChest: createChest,
        spawnRandomChest: spawnRandomChest,
        openChest: openChest,
        checkPlayerChestProximity: checkPlayerChestProximity
    };
})();