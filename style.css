body {
    margin: 0;
    padding: 0;
    background-color: #000;
    font-family: 'Courier New', monospace;
    overflow: hidden;
    color: #fff;
}

#gameContainer {
    position: relative;
    width: 1200px;
    height: 800px;
    margin: 20px auto;
    border: 2px solid #444;
}

#gameCanvas {
    background-color: #111;
    display: block;
}

#ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

#healthBar {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 200px;
    height: 30px;
    background-color: #333;
    border: 2px solid #555;
    border-radius: 5px;
    z-index: 10;
}

#healthFill {
    height: 100%;
    width: 100%;
    background: linear-gradient(to right, #ff0000, #00ff00);
    border-radius: 3px;
    transition: width 0.3s;
}

#healthText {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 1px black;
    z-index: 11;
}

#ammoInfo {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 20px;
    font-weight: bold;
    text-shadow: 1px 1px 1px black;
    z-index: 10;
}

#ammoCount {
    display: block;
}

#reloadText {
    display: block;
    color: #ffcc00;
    font-size: 16px;
}

.hidden {
    display: none !important;
}

#crosshair {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    pointer-events: none;
    z-index: 10;
}

#crosshair::before, #crosshair::after {
    content: '';
    position: absolute;
    background-color: rgba(255, 255, 255, 0.8);
}

#crosshair::before {
    width: 2px;
    height: 8px;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

#crosshair::after {
    width: 8px;
    height: 2px;
    top: 50%;
    right: -10px;
    transform: translateY(-50%);
}

#instructions {
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 100%;
    text-align: center;
    color: #aaa;
    font-size: 14px;
    z-index: 10;
}

#gameOver, #pauseMenu, #lootSelection {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: white;
    font-size: 36px;
    text-align: center;
    z-index: 20;
}

#gameOver div, #pauseMenu div, #lootSelection div {
    margin-bottom: 20px;
}

#gameOver button, #pauseMenu button {
    margin: 10px;
    padding: 10px 20px;
    font-size: 20px;
    background-color: #4169E1;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    pointer-events: auto;
}

#gameOver button:hover, #pauseMenu button:hover {
    background-color: #3159d1;
}

/* 背包系统 */
#inventory {
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.inventory-slot {
    width: 50px;
    height: 50px;
    border: 2px solid #666;
    background-color: rgba(50, 50, 50, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
    pointer-events: auto;
}

.inventory-slot.active {
    border-color: #ffff00;
    background-color: rgba(100, 100, 100, 0.9);
}

/* 战利品选择界面 */
#lootSelection {
    flex-direction: column;
}

.loot-title {
    font-size: 32px;
    margin-bottom: 30px;
    color: #ffcc00;
}

.loot-options {
    display: flex;
    gap: 30px;
}

.loot-option {
    width: 180px;
    height: 200px;
    background-color: #333;
    border: 2px solid #555;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: auto;
    transition: all 0.2s;
}

.loot-option:hover {
    transform: scale(1.05);
    border-color: #ffcc00;
    background-color: #444;
}

.loot-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.loot-name {
    font-size: 18px;
    text-align: center;
}

/* 交互提示 */
#interactionPrompt {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 18px;
    text-align: center;
    z-index: 15;
    pointer-events: none;
    border: 1px solid #444;
}

#promptText {
    display: block;
}

/* 教程消息 */
.tutorial-message {
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 30, 0, 0.9);
    color: #0f0;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 16px;
    text-align: center;
    z-index: 16;
    border: 1px solid #0f0;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.tutorial-message.hidden {
    display: none !important;
}

/* 临时消息 */
.temporary-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 100;
    font-size: 20px;
    pointer-events: none;
}