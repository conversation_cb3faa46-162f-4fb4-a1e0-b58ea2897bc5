// ====================
// 教程系统模块
// ====================
(function() {
    // 教程步骤
    const tutorialSteps = [
        {
            condition: () => GameState.level === 1 && GameState.gameTime < 300,
            message: "WASD键移动角色，鼠标控制瞄准"
        },
        {
            condition: () => GameState.level === 1 && GameState.zombies.length > 0 && 
                           GameState.zombiesKilled === 0 && GameState.gameTime > 300,
            message: "左键射击消灭丧尸"
        },
        {
            condition: () => GameState.level === 1 && GameState.weapon.ammo < GameState.weapon.maxAmmo && 
                           !GameState.weapon.isReloading,
            message: "按R键换弹，注意换弹期间无法射击"
        },
        {
            condition: () => GameState.level === 1 && 
                           ChestSystem.checkPlayerChestProximity() !== -1,
            message: "靠近宝箱按E键打开，获取奖励"
        },
        {
            condition: () => GameState.level === 1 && GameState.doors.length > 0,
            message: "靠近门按E键开关门"
        },
        {
            condition: () => GameState.level === 1 && GameState.items.length > 0,
            message: "靠近掉落物按E键拾取"
        }
    ];
    
    // 当前教程步骤
    let currentTutorialStep = 0;
    
    // 检查并显示教程提示
    function checkAndShowTutorial() {
        // 只在前几关显示教程
        if (GameState.level > 2) return;
        
        // 遍历教程步骤，找到符合条件的
        for (let i = currentTutorialStep; i < tutorialSteps.length; i++) {
            if (tutorialSteps[i].condition()) {
                showTutorialMessage(tutorialSteps[i].message);
                currentTutorialStep = i + 1;
                break;
            }
        }
    }
    
    // 显示教程消息
    function showTutorialMessage(message) {
        const tutorialElement = document.getElementById('tutorialMessage');
        if (tutorialElement) {
            tutorialElement.textContent = message;
            tutorialElement.classList.remove('hidden');
            
            // 3秒后隐藏
            setTimeout(() => {
                tutorialElement.classList.add('hidden');
            }, 3000);
        }
    }
    
    // 初始化教程系统
    function initTutorial() {
        // 创建教程元素（如果不存在）
        let tutorialElement = document.getElementById('tutorialMessage');
        if (!tutorialElement) {
            tutorialElement = document.createElement('div');
            tutorialElement.id = 'tutorialMessage';
            tutorialElement.className = 'tutorial-message hidden';
            document.getElementById('ui').appendChild(tutorialElement);
        }
    }
    
    // 导出教程系统接口
    window.TutorialSystem = {
        init: initTutorial,
        check: checkAndShowTutorial
    };
})();