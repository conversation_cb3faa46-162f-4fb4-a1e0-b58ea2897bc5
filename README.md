# 《末日回响：丧尸围城》项目规范文档

## 文件结构规范

```
zombie-game/
├── index.html              # 主页面
├── style.css               # 样式文件
├── js/                     # JavaScript源代码目录
│   ├── main.js             # 游戏主入口
│   ├── config/             # 配置文件
│   │   └── gameConfig.js   # 游戏配置
│   ├── core/               # 核心模块
│   │   ├── gameState.js    # 游戏状态管理
│   │   ├── gameLoop.js     # 游戏主循环
│   │   └── inputHandler.js # 输入处理
│   ├── entities/           # 游戏实体
│   │   ├── player.js       # 玩家模块
│   │   ├── zombie.js       # 丧尸模块
│   │   ├── bullet.js       # 子弹模块
│   │   └── item.js         # 物品模块
│   ├── systems/            # 游戏系统
│   │   ├── collision.js    # 碰撞检测系统
│   │   ├── particle.js     # 粒子系统
│   │   ├── audio.js        # 音效系统
│   │   ├── ui.js           # UI系统
│   │   ├── map.js          # 地图系统
│   │   └── door.js         # 门系统
│   ├── utils/              # 工具函数
│   │   └── helpers.js      # 通用辅助函数
│   └── levels/             # 关卡系统
│       └── levelManager.js # 关卡管理
└── assets/                 # 资源文件目录（预留）
```

## 模块化设计规范

### 1. 模块定义规范
每个模块应遵循以下结构：
```javascript
// 模块名称
(function() {
    // 私有变量和函数
    
    // 公共接口
    window.ModuleName = {
        // 公共方法和属性
    };
})();
```

### 2. 依赖管理
- 模块间依赖通过全局对象访问
- 核心模块（core/）不应依赖其他模块
- 实体模块（entities/）可依赖核心模块和系统模块
- 系统模块（systems/）可依赖核心模块

### 3. 命名规范
- 文件名使用驼峰命名法
- 类名使用帕斯卡命名法（首字母大写）
- 函数名和变量名使用驼峰命名法
- 常量使用全大写字母和下划线分隔

### 4. 注释规范
- 每个模块顶部添加模块说明注释
- 每个函数添加JSDoc格式注释
- 复杂逻辑添加行内注释说明

## 开发流程规范

1. 创建新功能时，先评估是否需要新模块
2. 新模块按照规范创建文件和目录
3. 模块间接口通过文档说明
4. 功能开发完成后进行单元测试
5. 提交代码前进行代码审查

## 扩展性规范

1. 新增系统功能应创建独立模块
2. 配置项应放在config目录中
3. 工具函数应放在utils目录中
4. 保持模块间的低耦合性