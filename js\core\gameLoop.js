// ====================
// 游戏主循环模块
// ====================
(function() {
    // 游戏主循环
    function gameLoop() {
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        if (!GameState.isPaused && !GameState.showingLoot) {
            Player.update();
            Zombie.update();
            Bullet.update();
            DoorSystem.updateDoors(); // 更新门的动画
            ParticleSystem.updateParticles();
            UISystem.checkPlayerZombieCollision();
            TutorialSystem.check(); // 检查并显示教程
            CameraSystem.update(); // 更新摄像机
            CameraSystem.adaptiveZoom(); // 自适应缩放
            GameState.gameTime++;
        }
        
        RenderSystem.render(ctx);
        requestAnimationFrame(gameLoop);
    }

    // 初始化游戏
    function initGame() {
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        AudioSystem.init();
        TutorialSystem.init(); // 初始化教程系统
        LevelManager.initLevel(1);

        // 初始化摄像机位置
        CameraSystem.setPosition(GameState.player.x, GameState.player.y);

        InputHandler.setupEventListeners();
        gameLoop();
    }

    // 导出游戏主循环接口
    window.GameLoop = {
        init: initGame,
        loop: gameLoop
    };
})();