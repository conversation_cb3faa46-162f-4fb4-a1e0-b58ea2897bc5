// ====================
// 渲染系统模块
// ====================
(function() {
    // 绘制交互提示
    function drawInteractionPrompt(ctx) {
        // 检查是否有可拾取的物品
        let nearestItem = null;
        let minItemDistance = Infinity;
        
        for (const item of GameState.items) {
            const distance = Math.sqrt(
                Math.pow(item.x - GameState.player.x, 2) +
                Math.pow(item.y - GameState.player.y, 2)
            );
            
            if (distance < CONFIG.ITEM_PICKUP_DISTANCE && distance < minItemDistance) {
                minItemDistance = distance;
                nearestItem = item;
            }
        }
        
        // 检查是否有可交互的门
        let nearestDoor = null;
        let minDoorDistance = Infinity;
        
        for (const door of GameState.doors) {
            const distance = Math.sqrt(
                Math.pow((door.x + door.width/2) - GameState.player.x, 2) +
                Math.pow((door.y + door.height/2) - GameState.player.y, 2)
            );
            
            if (distance < CONFIG.DOOR_INTERACT_DISTANCE && distance < minDoorDistance) {
                minDoorDistance = distance;
                nearestDoor = door;
            }
        }
        
        // 检查是否有可打开的宝箱
        let nearestChest = null;
        let minChestDistance = Infinity;
        
        for (const chest of GameState.chests) {
            if (chest.isOpened) continue;
            
            const distance = Math.sqrt(
                Math.pow(chest.x - GameState.player.x, 2) +
                Math.pow(chest.y - GameState.player.y, 2)
            );
            
            if (distance < CONFIG.DOOR_INTERACT_DISTANCE && distance < minChestDistance) {
                minChestDistance = distance;
                nearestChest = chest;
            }
        }
        
        // 绘制提示文本
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = '18px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        if (nearestChest && (!nearestItem || minChestDistance < minItemDistance) && 
            (!nearestDoor || minChestDistance < minDoorDistance)) {
            // 显示打开宝箱提示
            ctx.fillText(`按 E 打开 ${nearestChest.name}`, 600, 750);
        } else if (nearestItem && (!nearestDoor || minItemDistance < minDoorDistance)) {
            // 显示拾取提示
            ctx.fillText(`按 E 拾取 ${nearestItem.name}`, 600, 750);
        } else if (nearestDoor) {
            // 显示开关门提示
            const action = nearestDoor.open ? "关闭" : "打开";
            ctx.fillText(`按 E ${action}门`, 600, 750);
        }
    }
    
    // 渲染函数
    function render(ctx) {
        // 清除画布
        ctx.clearRect(0, 0, 1200, 800);
        
        // 绘制背景
        ctx.fillStyle = 'rgba(20, 20, 30, 1)';
        ctx.fillRect(0, 0, 1200, 800);
        
        // 绘制环境光源
        for (const light of GameState.lights) {
            const gradient = ctx.createRadialGradient(
                light.x, light.y, 0,
                light.x, light.y, light.radius
            );
            gradient.addColorStop(0, `rgba(255, 255, 200, ${light.intensity})`);
            gradient.addColorStop(1, 'rgba(255, 255, 200, 0)');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(light.x, light.y, light.radius, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // 绘制手电筒光源
        const flashlightGradient = ctx.createRadialGradient(
            GameState.player.x, GameState.player.y, 0,
            GameState.player.x, GameState.player.y, 250
        );
        flashlightGradient.addColorStop(0, 'rgba(255, 255, 200, 0.6)');
        flashlightGradient.addColorStop(1, 'rgba(255, 255, 200, 0)');
        
        ctx.fillStyle = flashlightGradient;
        ctx.beginPath();
        ctx.arc(GameState.player.x, GameState.player.y, 250, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制墙壁
        ctx.fillStyle = '#555555';
        for (const wall of GameState.walls) {
            ctx.fillRect(wall.x, wall.y, wall.width, wall.height);
        }
        
        // 绘制门
        for (const door of GameState.doors) {
            if (door.horizontal) {
                ctx.fillStyle = door.open ? '#8B4513' : '#654321';
                ctx.fillRect(door.x, door.y, door.width, door.height);
                
                // 绘制门框
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(door.x, door.y, door.width, door.height);
            } else {
                ctx.fillStyle = door.open ? '#8B4513' : '#654321';
                ctx.fillRect(door.x, door.y, door.width, door.height);
                
                // 绘制门框
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(door.x, door.y, door.width, door.height);
            }
        }
        
        // 绘制物品
        for (const item of GameState.items) {
            ctx.font = '24px Arial';
            ctx.fillStyle = '#ffffff';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(item.icon, item.x, item.y);
        }
        
        // 绘制宝箱
        for (const chest of GameState.chests) {
            // 宝箱主体
            ctx.fillStyle = chest.rarity === 'legendary' ? '#FFD700' : 
                           chest.rarity === 'rare' ? '#87CEEB' : '#A9A9A9';
            ctx.fillRect(chest.x - chest.width/2, chest.y - chest.height/2, chest.width, chest.height);
            
            // 宝箱盖子
            ctx.fillStyle = chest.rarity === 'legendary' ? '#FFA500' : 
                           chest.rarity === 'rare' ? '#4682B4' : '#696969';
            ctx.fillRect(chest.x - chest.width/2, chest.y - chest.height/2 - 5, chest.width, 10);
            
            // 宝箱图标
            ctx.font = '20px Arial';
            ctx.fillStyle = '#ffffff';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(chest.icon, chest.x, chest.y);
        }
        
        // 绘制丧尸
        for (const zombie of GameState.zombies) {
            // 丧尸主体
            const zombieConfig = CONFIG.ZOMBIE_TYPES[zombie.type];
            ctx.fillStyle = zombieConfig.color;
            ctx.fillRect(zombie.x - zombie.width/2, zombie.y - zombie.height/2, zombie.width, zombie.height);
            
            // 丧尸眼睛
            ctx.fillStyle = 'red';
            ctx.fillRect(zombie.x - 5, zombie.y - 5, 3, 3);
            ctx.fillRect(zombie.x + 2, zombie.y - 5, 3, 3);
            
            // 丧尸血条
            const healthPercent = (zombie.health / zombie.maxHealth) * 100;
            ctx.fillStyle = '#333';
            ctx.fillRect(zombie.x - 15, zombie.y - 25, 30, 5);
            ctx.fillStyle = healthPercent > 50 ? 'green' : healthPercent > 25 ? 'orange' : 'red';
            ctx.fillRect(zombie.x - 15, zombie.y - 25, 30 * (healthPercent / 100), 5);
        }
        
        // 绘制子弹
        ctx.fillStyle = '#ffff00';
        for (const bullet of GameState.bullets) {
            ctx.beginPath();
            ctx.arc(bullet.x, bullet.y, bullet.width, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // 绘制粒子
        for (const particle of GameState.particles) {
            particle.draw(ctx);
        }
        
        // 绘制玩家
        ctx.save();
        ctx.translate(GameState.player.x, GameState.player.y);
        ctx.rotate(GameState.player.angle);
        
        // 玩家身体
        ctx.fillStyle = '#4169E1';
        ctx.fillRect(-15, -15, 30, 30);
        
        // 玩家武器
        ctx.fillStyle = '#2F4F4F';
        ctx.fillRect(15, -3, 20, 6);
        
        // 瞄准线
        if (GameState.mouse.isAiming) {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(35, 0);
            ctx.lineTo(100, 0);
            ctx.stroke();
        }
        
        ctx.restore();
        
        // 绘制交互提示
        drawInteractionPrompt(ctx);
        
        // 绘制UI信息
        ctx.fillStyle = 'white';
        ctx.font = '20px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('得分: ' + GameState.score, 20, 70);
        ctx.fillText('关卡: ' + GameState.level, 20, 100);
        ctx.fillText('丧尸: ' + GameState.zombiesKilled + '/' + GameState.zombiesToKill, 20, 130);
        
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        ctx.font = '16px Arial';
        ctx.textAlign = 'right';
        ctx.fillText('ESC: 暂停', 1180, 30);
    }

    // 导出渲染系统接口
    window.RenderSystem = {
        render: render
    };
})();