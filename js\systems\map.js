// ====================
// 地图系统模块
// ====================
(function() {
    // ====================
    // 房间类
    // ====================
    class Room {
        constructor(x, y, width, height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.centerX = Math.floor(x + width / 2);
            this.centerY = Math.floor(y + height / 2);
        }
        
        // 检查是否与另一个房间重叠
        intersects(other) {
            return (
                this.x < other.x + other.width &&
                this.x + this.width > other.x &&
                this.y < other.y + other.height &&
                this.y + this.height > other.y
            );
        }
        
        // 获取房间的边缘坐标
        getEdges() {
            return {
                left: this.x,
                right: this.x + this.width,
                top: this.y,
                bottom: this.y + this.height
            };
        }
        
        // 检查点是否在房间内
        containsPoint(x, y) {
            return (
                x >= this.x && 
                x < this.x + this.width && 
                y >= this.y && 
                y < this.y + this.height
            );
        }
    }

    // 生成平衡性优化的地图
    function generateBalancedMap() {
        // 初始化地图
        const map = [];
        for (let y = 0; y < CONFIG.MAP_HEIGHT; y++) {
            map[y] = [];
            for (let x = 0; x < CONFIG.MAP_WIDTH; x++) {
                // 默认设置为墙壁
                map[y][x] = TILE_TYPES.WALL;
            }
        }
        
        // 生成房间 - 增大房间尺寸
        const rooms = [];
        const maxRooms = 6 + Math.floor(Math.random() * 4); // 6-9个房间
        const minRoomSize = 10;  // 增大最小房间尺寸
        const maxRoomSize = 20; // 增大最大房间尺寸
        
        for (let i = 0; i < maxRooms; i++) {
            // 随机房间大小
            const width = Math.floor(Math.random() * (maxRoomSize - minRoomSize)) + minRoomSize;
            const height = Math.floor(Math.random() * (maxRoomSize - minRoomSize)) + minRoomSize;
            
            // 随机位置
            const x = Math.floor(Math.random() * (CONFIG.MAP_WIDTH - width - 4)) + 2;
            const y = Math.floor(Math.random() * (CONFIG.MAP_HEIGHT - height - 4)) + 2;
            
            const newRoom = new Room(x, y, width, height);
            
            // 检查是否与现有房间重叠
            let overlaps = false;
            for (const room of rooms) {
                if (newRoom.intersects(room)) {
                    overlaps = true;
                    break;
                }
            }
            
            // 如果不重叠，添加房间
            if (!overlaps) {
                rooms.push(newRoom);
                
                // 在地图上标记房间区域为空地
                for (let ry = y; ry < y + height; ry++) {
                    for (let rx = x; rx < x + width; rx++) {
                        map[ry][rx] = TILE_TYPES.EMPTY;
                    }
                }
            }
        }
        
        // 连接房间
        if (rooms.length > 1) {
            // 从第一个房间开始连接
            for (let i = 1; i < rooms.length; i++) {
                const prevRoom = rooms[i - 1];
                const currentRoom = rooms[i];
                
                // 创建L型走廊连接两个房间
                // 先水平连接，再垂直连接
                const prevX = prevRoom.centerX;
                const prevY = prevRoom.centerY;
                const currentX = currentRoom.centerX;
                const currentY = currentRoom.centerY;
                
                // 水平隧道 (增加宽度到3格)
                const minX = Math.min(prevX, currentX);
                const maxX = Math.max(prevX, currentX);
                for (let x = minX; x <= maxX; x++) {
                    if (prevY >= 2 && prevY < CONFIG.MAP_HEIGHT - 2 && 
                        x >= 2 && x < CONFIG.MAP_WIDTH - 2) {
                        // 创建3格宽的走廊
                        map[prevY][x] = TILE_TYPES.EMPTY;
                        map[prevY-1][x] = TILE_TYPES.EMPTY;
                        map[prevY+1][x] = TILE_TYPES.EMPTY;
                    }
                }
                
                // 垂直隧道 (增加宽度到3格)
                const minY = Math.min(prevY, currentY);
                const maxY = Math.max(prevY, currentY);
                for (let y = minY; y <= maxY; y++) {
                    if (y >= 2 && y < CONFIG.MAP_HEIGHT - 2 && 
                        currentX >= 2 && currentX < CONFIG.MAP_WIDTH - 2) {
                        // 创建3格宽的走廊
                        map[y][currentX] = TILE_TYPES.EMPTY;
                        map[y][currentX-1] = TILE_TYPES.EMPTY;
                        map[y][currentX+1] = TILE_TYPES.EMPTY;
                    }
                }
            }
        }
        
        // 在连接点创建门
        for (let i = 1; i < rooms.length; i++) {
            const prevRoom = rooms[i - 1];
            const currentRoom = rooms[i];
            
            // 计算连接点（走廊的拐弯点）
            const doorX = currentRoom.centerX;
            const doorY = prevRoom.centerY;
            
            // 确定门的方向
            const isHorizontal = Math.abs(prevRoom.centerX - currentRoom.centerX) > Math.abs(prevRoom.centerY - currentRoom.centerY);
            
            // 标记门的位置 - 放置在走廊与房间的交界处，并设置随机初始状态
            if (isHorizontal) {
                // 水平门 - 放置在走廊与房间的交界处
                if (doorX < currentRoom.x + currentRoom.width / 2) {
                    // 门在房间左侧入口
                    map[doorY][currentRoom.x-1] = TILE_TYPES.DOOR_HORIZONTAL;
                } else {
                    // 门在房间右侧入口
                    map[doorY][currentRoom.x + currentRoom.width] = TILE_TYPES.DOOR_HORIZONTAL;
                }
            } else {
                // 垂直门 - 放置在走廊与房间的交界处
                if (doorY < currentRoom.y + currentRoom.height / 2) {
                    // 门在房间上侧入口
                    map[currentRoom.y-1][doorX] = TILE_TYPES.DOOR_VERTICAL;
                } else {
                    // 门在房间下侧入口
                    map[currentRoom.y + currentRoom.height][doorX] = TILE_TYPES.DOOR_VERTICAL;
                }
            }
        }
        
        return { map, rooms };
    }

    // 根据地图生成墙壁和门
    function createWallsAndDoorsFromMap(mapData) {
        const { map, rooms } = mapData;
        GameState.map = map;
        GameState.rooms = rooms;
        GameState.walls = [];
        GameState.doors = [];
        
        // 确定起始房间（第一个房间）
        if (rooms.length > 0) {
            GameState.startRoom = rooms[0];
        }
        
        // 创建墙壁和门
        for (let y = 0; y < CONFIG.MAP_HEIGHT; y++) {
            for (let x = 0; x < CONFIG.MAP_WIDTH; x++) {
                const tileX = x * CONFIG.TILE_SIZE;
                const tileY = y * CONFIG.TILE_SIZE;
                
                if (map[y][x] === TILE_TYPES.WALL) {
                    GameState.walls.push({
                        x: tileX,
                        y: tileY,
                        width: CONFIG.TILE_SIZE,
                        height: CONFIG.TILE_SIZE
                    });
                } else if (map[y][x] === TILE_TYPES.DOOR_HORIZONTAL) {
                    GameState.doors.push({
                        x: tileX,
                        y: tileY + CONFIG.TILE_SIZE/2 - 5,
                        width: CONFIG.TILE_SIZE,
                        height: 10,
                        open: Math.random() > 0.5, // 随机初始状态
                        horizontal: true
                    });
                } else if (map[y][x] === TILE_TYPES.DOOR_VERTICAL) {
                    GameState.doors.push({
                        x: tileX + CONFIG.TILE_SIZE/2 - 5,
                        y: tileY,
                        width: 10,
                        height: CONFIG.TILE_SIZE,
                        open: Math.random() > 0.5, // 随机初始状态
                        horizontal: false
                    });
                }
            }
        }
    }

    // 导出地图系统接口
    window.MapSystem = {
        Room: Room,
        generateBalancedMap: generateBalancedMap,
        createWallsAndDoorsFromMap: createWallsAndDoorsFromMap
    };
})();