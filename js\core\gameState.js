// ====================
// 游戏状态管理模块
// ====================
(function() {
    const gameState = {
        // 玩家状态
        player: {
            x: 600,
            y: 400,
            width: 30,
            height: 30,
            health: 100,
            maxHealth: 100,
            angle: 0,
            isSprinting: false,
            speedBoost: 0
        },
        
        // 武器状态
        weapon: {
            type: "PISTOL",
            ammo: 12,
            maxAmmo: 12,
            isReloading: false,
            lastFired: 0
        },
        
        // 游戏对象
        zombies: [],
        bullets: [],
        walls: [],
        doors: [],
        items: [],
        chests: [], // 宝箱
        lights: [],
        particles: [],
        
        // 地图系统
        map: [],
        rooms: [],
        startRoom: null, // 起始房间
        
        // 背包系统
        inventory: [
            null, null, null, null, null, null
        ],
        activeItemSlot: -1,
        
        // 输入状态
        keys: {},
        mouse: {
            x: 0,
            y: 0,
            isAiming: false,
            isShooting: false,
            lastShot: 0
        },
        
        // 游戏进度
        gameTime: 0,
        score: 0,
        level: 1,
        zombiesKilled: 0,
        zombiesToKill: 0,
        isPaused: false,
        showingLoot: false
    };

    // 导出游戏状态
    window.GameState = gameState;
})();