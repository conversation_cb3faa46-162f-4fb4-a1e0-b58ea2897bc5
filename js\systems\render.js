// ====================
// 渲染系统模块
// ====================
(function() {
    // 绘制门的函数
    function drawDoor(ctx, door) {
        const config = door.config;

        // 计算门的实际绘制位置（考虑开门动画）
        let doorRect = {
            x: door.x,
            y: door.y,
            width: door.width,
            height: door.height
        };

        // 根据开门进度调整门的绘制
        if (door.orientation === 'horizontal') {
            // 水平门：从两边向中间收缩
            const openWidth = door.width * door.openProgress;
            doorRect.width = door.width - openWidth;
            doorRect.x = door.x + openWidth / 2;
        } else {
            // 垂直门：从上下向中间收缩
            const openHeight = door.height * door.openProgress;
            doorRect.height = door.height - openHeight;
            doorRect.y = door.y + openHeight / 2;
        }

        // 绘制门框（始终显示）
        ctx.strokeStyle = config.frameColor;
        ctx.lineWidth = config.frameWidth;
        ctx.strokeRect(door.x, door.y, door.width, door.height);

        // 绘制门板（根据开门进度）
        if (doorRect.width > 0 && doorRect.height > 0) {
            ctx.fillStyle = config.color;
            ctx.fillRect(doorRect.x, doorRect.y, doorRect.width, doorRect.height);

            // 绘制门把手（只在门没有完全打开时显示）
            if (door.openProgress < 0.9) {
                ctx.fillStyle = config.handleColor;
                if (door.orientation === 'horizontal') {
                    // 水平门把手
                    const handleX = doorRect.x + doorRect.width - 8;
                    const handleY = doorRect.y + doorRect.height / 2 - 3;
                    ctx.fillRect(handleX, handleY, 6, 6);
                } else {
                    // 垂直门把手
                    const handleX = doorRect.x + doorRect.width / 2 - 3;
                    const handleY = doorRect.y + doorRect.height - 8;
                    ctx.fillRect(handleX, handleY, 6, 6);
                }
            }
        }

        // 绘制门的阴影效果
        if (door.openProgress < 1.0) {
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;

            // 重新绘制门板以应用阴影
            ctx.fillStyle = config.color;
            ctx.fillRect(doorRect.x, doorRect.y, doorRect.width, doorRect.height);

            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        }
    }
    // 绘制交互提示
    function drawInteractionPrompt(ctx) {
        // 检查是否有可拾取的物品
        let nearestItem = null;
        let minItemDistance = Infinity;
        
        for (const item of GameState.items) {
            const distance = Math.sqrt(
                Math.pow(item.x - GameState.player.x, 2) +
                Math.pow(item.y - GameState.player.y, 2)
            );
            
            if (distance < CONFIG.ITEM_PICKUP_DISTANCE && distance < minItemDistance) {
                minItemDistance = distance;
                nearestItem = item;
            }
        }
        
        // 检查是否有可交互的门（使用新的门系统）
        const nearestDoor = DoorSystem.getNearestInteractableDoor();
        
        // 检查是否有可打开的宝箱
        let nearestChest = null;
        let minChestDistance = Infinity;
        
        for (const chest of GameState.chests) {
            if (chest.isOpened) continue;
            
            const distance = Math.sqrt(
                Math.pow(chest.x - GameState.player.x, 2) +
                Math.pow(chest.y - GameState.player.y, 2)
            );
            
            if (distance < CONFIG.DOOR_INTERACT_DISTANCE && distance < minChestDistance) {
                minChestDistance = distance;
                nearestChest = chest;
            }
        }
        
        // 绘制提示文本
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = '18px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        if (nearestChest && (!nearestItem || minChestDistance < minItemDistance) && 
            (!nearestDoor || minChestDistance < minDoorDistance)) {
            // 显示打开宝箱提示
            ctx.fillText(`按 E 打开 ${nearestChest.name}`, 600, 750);
        } else if (nearestItem && (!nearestDoor || minItemDistance < minDoorDistance)) {
            // 显示拾取提示
            ctx.fillText(`按 E 拾取 ${nearestItem.name}`, 600, 750);
        } else if (nearestDoor) {
            // 显示开关门提示
            const action = nearestDoor.open ? "关闭" : "打开";
            ctx.fillText(`按 E ${action}门`, 600, 750);
        }
    }
    
    // 渲染函数
    function render(ctx) {
        // 清除画布
        ctx.clearRect(0, 0, CONFIG.CAMERA.VIEWPORT_WIDTH, CONFIG.CAMERA.VIEWPORT_HEIGHT);

        // 绘制背景
        ctx.fillStyle = 'rgba(20, 20, 30, 1)';
        ctx.fillRect(0, 0, CONFIG.CAMERA.VIEWPORT_WIDTH, CONFIG.CAMERA.VIEWPORT_HEIGHT);

        // 应用摄像机变换
        CameraSystem.applyTransform(ctx);

        // 绘制环境光源
        for (const light of GameState.lights) {
            if (CameraSystem.isInView(light.x - light.radius, light.y - light.radius, light.radius * 2, light.radius * 2)) {
                const gradient = ctx.createRadialGradient(
                    light.x, light.y, 0,
                    light.x, light.y, light.radius
                );
                gradient.addColorStop(0, `rgba(255, 255, 200, ${light.intensity})`);
                gradient.addColorStop(1, 'rgba(255, 255, 200, 0)');

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(light.x, light.y, light.radius, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        // 绘制手电筒光源
        const flashlightGradient = ctx.createRadialGradient(
            GameState.player.x, GameState.player.y, 0,
            GameState.player.x, GameState.player.y, 250
        );
        flashlightGradient.addColorStop(0, 'rgba(255, 255, 200, 0.6)');
        flashlightGradient.addColorStop(1, 'rgba(255, 255, 200, 0)');

        ctx.fillStyle = flashlightGradient;
        ctx.beginPath();
        ctx.arc(GameState.player.x, GameState.player.y, 250, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制墙壁（只绘制视野内的）
        ctx.fillStyle = '#555555';
        for (const wall of GameState.walls) {
            if (CameraSystem.isInView(wall.x, wall.y, wall.width, wall.height)) {
                ctx.fillRect(wall.x, wall.y, wall.width, wall.height);
            }
        }
        
        // 绘制门（新的门系统）
        for (const door of GameState.doors) {
            if (CameraSystem.isInView(door.x, door.y, door.width, door.height)) {
                drawDoor(ctx, door);
            }
        }
        
        // 绘制物品（只绘制视野内的）
        for (const item of GameState.items) {
            if (CameraSystem.isInView(item.x - 15, item.y - 15, 30, 30)) {
                ctx.font = '24px Arial';
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(item.icon, item.x, item.y);
            }
        }

        // 绘制宝箱（只绘制视野内的）
        for (const chest of GameState.chests) {
            if (CameraSystem.isInView(chest.x - chest.width/2, chest.y - chest.height/2, chest.width, chest.height)) {
                // 宝箱主体
                ctx.fillStyle = chest.rarity === 'legendary' ? '#FFD700' :
                               chest.rarity === 'rare' ? '#87CEEB' : '#A9A9A9';
                ctx.fillRect(chest.x - chest.width/2, chest.y - chest.height/2, chest.width, chest.height);

                // 宝箱盖子
                ctx.fillStyle = chest.rarity === 'legendary' ? '#FFA500' :
                               chest.rarity === 'rare' ? '#4682B4' : '#696969';
                ctx.fillRect(chest.x - chest.width/2, chest.y - chest.height/2 - 5, chest.width, 10);

                // 宝箱图标
                ctx.font = '20px Arial';
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(chest.icon, chest.x, chest.y);
            }
        }
        
        // 绘制丧尸（只绘制视野内的）
        for (const zombie of GameState.zombies) {
            if (CameraSystem.isInView(zombie.x - zombie.width/2, zombie.y - zombie.height/2, zombie.width, zombie.height)) {
                // 丧尸主体
                const zombieConfig = CONFIG.ZOMBIE_TYPES[zombie.type];
                ctx.fillStyle = zombieConfig.color;
                ctx.fillRect(zombie.x - zombie.width/2, zombie.y - zombie.height/2, zombie.width, zombie.height);

                // 丧尸眼睛
                ctx.fillStyle = 'red';
                ctx.fillRect(zombie.x - 5, zombie.y - 5, 3, 3);
                ctx.fillRect(zombie.x + 2, zombie.y - 5, 3, 3);

                // 丧尸血条
                const healthPercent = (zombie.health / zombie.maxHealth) * 100;
                ctx.fillStyle = '#333';
                ctx.fillRect(zombie.x - 15, zombie.y - 25, 30, 5);
                ctx.fillStyle = healthPercent > 50 ? 'green' : healthPercent > 25 ? 'orange' : 'red';
                ctx.fillRect(zombie.x - 15, zombie.y - 25, 30 * (healthPercent / 100), 5);
            }
        }

        // 绘制子弹（只绘制视野内的）
        ctx.fillStyle = '#ffff00';
        for (const bullet of GameState.bullets) {
            if (CameraSystem.isInView(bullet.x - bullet.width, bullet.y - bullet.width, bullet.width * 2, bullet.width * 2)) {
                ctx.beginPath();
                ctx.arc(bullet.x, bullet.y, bullet.width, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        // 绘制粒子（只绘制视野内的）
        for (const particle of GameState.particles) {
            if (CameraSystem.isInView(particle.x - 10, particle.y - 10, 20, 20)) {
                particle.draw(ctx);
            }
        }
        
        // 绘制玩家
        ctx.save();
        ctx.translate(GameState.player.x, GameState.player.y);
        ctx.rotate(GameState.player.angle);

        // 玩家身体
        ctx.fillStyle = '#4169E1';
        ctx.fillRect(-15, -15, 30, 30);

        // 玩家武器
        ctx.fillStyle = '#2F4F4F';
        ctx.fillRect(15, -3, 20, 6);

        // 瞄准线
        if (GameState.mouse.isAiming) {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(35, 0);
            ctx.lineTo(100, 0);
            ctx.stroke();
        }

        ctx.restore();

        // 恢复摄像机变换
        CameraSystem.resetTransform(ctx);

        // 绘制交互提示（在屏幕坐标系中）
        drawInteractionPrompt(ctx);
        
        // 绘制UI信息
        ctx.fillStyle = 'white';
        ctx.font = '20px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('得分: ' + GameState.score, 20, 70);
        ctx.fillText('关卡: ' + GameState.level, 20, 100);
        ctx.fillText('丧尸: ' + GameState.zombiesKilled + '/' + GameState.zombiesToKill, 20, 130);
        
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        ctx.font = '16px Arial';
        ctx.textAlign = 'right';
        ctx.fillText('ESC: 暂停', 1180, 30);
    }

    // 导出渲染系统接口
    window.RenderSystem = {
        render: render
    };
})();