// ====================
// 输入处理模块
// ====================
(function() {
    // 设置事件监听器
    function setupEventListeners() {
        // 键盘事件
        window.addEventListener('keydown', (e) => {
            // 暂停/继续游戏
            if (e.key === 'Escape') {
                if (!GameState.showingLoot) {
                    GameState.isPaused = !GameState.isPaused;
                    document.getElementById('pauseMenu').style.display = GameState.isPaused ? 'flex' : 'none';
                }
                return;
            }
            
            if (GameState.isPaused || GameState.showingLoot) return;
            
            GameState.keys[e.key.toLowerCase()] = true;
            
            // 换弹
            if (e.key.toLowerCase() === 'r' && !GameState.weapon.isReloading && GameState.weapon.ammo < GameState.weapon.maxAmmo) {
                reloadWeapon();
            }
            
            // 互动（开关门/拾取物品/打开宝箱）
            if (e.key.toLowerCase() === 'e') {
                // 检查是否有宝箱可以打开
                const chestIndex = ChestSystem.checkPlayerChestProximity();
                if (chestIndex !== -1) {
                    ChestSystem.openChest(chestIndex);
                } else {
                    // 否则执行原有操作
                    DoorSystem.interactWithDoor();
                    ItemSystem.pickupNearbyItem();
                }
            }
            
            // 使用物品（F键）
            if (e.key.toLowerCase() === 'f' && GameState.activeItemSlot >= 0) {
                ItemSystem.useItem(GameState.activeItemSlot);
            }
            
            // 数字键选择背包物品
            if (e.key >= '1' && e.key <= '6') {
                const slotIndex = parseInt(e.key) - 1;
                GameState.activeItemSlot = GameState.activeItemSlot === slotIndex ? -1 : slotIndex;
                ItemSystem.updateInventoryUI();
            }

            // 摄像机缩放控制
            if (e.key === '=' || e.key === '+') {
                const currentZoom = CameraSystem.getCamera().zoom;
                CameraSystem.setZoom(currentZoom * 1.1);
            }
            if (e.key === '-' || e.key === '_') {
                const currentZoom = CameraSystem.getCamera().zoom;
                CameraSystem.setZoom(currentZoom * 0.9);
            }
        });
        
        window.addEventListener('keyup', (e) => {
            GameState.keys[e.key.toLowerCase()] = false;
        });
        
        // 鼠标事件
        const canvas = document.getElementById('gameCanvas');
        canvas.addEventListener('mousemove', (e) => {
            if (GameState.isPaused || GameState.showingLoot) return;
            
            const rect = canvas.getBoundingClientRect();
            const screenX = e.clientX - rect.left;
            const screenY = e.clientY - rect.top;

            // 将屏幕坐标转换为世界坐标
            const worldPos = CameraSystem.screenToWorld(screenX, screenY);
            GameState.mouse.x = screenX; // 保留屏幕坐标用于UI
            GameState.mouse.y = screenY;

            // 计算玩家朝向角度（使用世界坐标）
            const dx = worldPos.x - GameState.player.x;
            const dy = worldPos.y - GameState.player.y;
            GameState.player.angle = Math.atan2(dy, dx);
        });
        
        canvas.addEventListener('mousedown', (e) => {
            if (GameState.isPaused || GameState.showingLoot) return;
            
            if (e.button === 0) { // 左键射击
                Bullet.shoot();
            } else if (e.button === 2) { // 右键瞄准
                GameState.mouse.isAiming = true;
            }
        });
        
        canvas.addEventListener('mouseup', (e) => {
            if (e.button === 2) { // 右键释放
                GameState.mouse.isAiming = false;
            }
        });
        
        // 阻止右键菜单
        canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
        
        // UI按钮事件
        document.getElementById('restartButton').addEventListener('click', () => {
            document.getElementById('gameOver').style.display = 'none';
            LevelManager.initLevel(1);
            GameState.isPaused = false;
        });
        
        document.getElementById('resumeButton').addEventListener('click', () => {
            GameState.isPaused = false;
            document.getElementById('pauseMenu').style.display = 'none';
        });
        
        document.getElementById('restartButtonPause').addEventListener('click', () => {
            document.getElementById('pauseMenu').style.display = 'none';
            LevelManager.initLevel(1);
            GameState.isPaused = false;
        });
        
        // 背包槽位点击事件
        for (let i = 0; i < 6; i++) {
            document.getElementById(`slot-${i}`).addEventListener('click', () => {
                if (GameState.isPaused || GameState.showingLoot) return;
                
                GameState.activeItemSlot = GameState.activeItemSlot === i ? -1 : i;
                ItemSystem.updateInventoryUI();
            });
        }
        
        // 战利品选择事件
        document.getElementById('loot1').addEventListener('click', () => LevelManager.selectLoot(0));
        document.getElementById('loot2').addEventListener('click', () => LevelManager.selectLoot(1));
        document.getElementById('loot3').addEventListener('click', () => LevelManager.selectLoot(2));
    }

    // 换弹
    function reloadWeapon() {
        const weaponConfig = CONFIG.WEAPONS[GameState.weapon.type];
        
        if (GameState.weapon.ammo < GameState.weapon.maxAmmo && !GameState.weapon.isReloading) {
            GameState.weapon.isReloading = true;
            AudioSystem.play('reload');
            document.getElementById('reloadText').classList.remove('hidden');
            
            setTimeout(() => {
                GameState.weapon.ammo = GameState.weapon.maxAmmo;
                GameState.weapon.isReloading = false;
                document.getElementById('reloadText').classList.add('hidden');
                UISystem.updateAmmoUI();
            }, weaponConfig.reloadTime);
        }
    }

    // 导出输入处理接口
    window.InputHandler = {
        setupEventListeners: setupEventListeners,
        reloadWeapon: reloadWeapon
    };
})();