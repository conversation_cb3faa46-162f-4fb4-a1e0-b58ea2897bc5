<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>末日回响：丧尸围城</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="1200" height="800"></canvas>
        <div id="ui">
            <div id="healthBar">
                <div id="healthFill"></div>
                <span id="healthText">100/100</span>
            </div>
            <div id="ammoInfo">
                <span id="ammoCount">12/12 手枪</span>
                <span id="reloadText" class="hidden">换弹中...</span>
            </div>
            <div id="crosshair"></div>
            <!-- 背包UI -->
            <div id="inventory">
                <div class="inventory-slot" id="slot-0"></div>
                <div class="inventory-slot" id="slot-1"></div>
                <div class="inventory-slot" id="slot-2"></div>
                <div class="inventory-slot" id="slot-3"></div>
                <div class="inventory-slot" id="slot-4"></div>
                <div class="inventory-slot" id="slot-5"></div>
            </div>
            <!-- 交互提示 -->
            <div id="interactionPrompt" class="hidden">
                <span id="promptText"></span>
            </div>
        </div>
        <div id="instructions">
            <p>WASD: 移动 | 鼠标: 瞄准/射击 | Shift: 疾跑 | R: 换弹 | E: 互动(开关门/拾取) | F: 使用物品 | ESC: 暂停</p>
        </div>
        <div id="gameOver">
            <div>游戏结束</div>
            <div id="finalScore">得分: 0</div>
            <div id="finalLevel">关卡: 1</div>
            <div id="lootInfo">获得战利品：</div>
            <button id="restartButton">重新开始</button>
        </div>
        <div id="pauseMenu">
            <div>游戏暂停</div>
            <button id="resumeButton">继续游戏</button>
            <button id="restartButtonPause">重新开始</button>
        </div>
        <!-- 战利品选择界面 -->
        <div id="lootSelection" class="hidden">
            <div class="loot-title">选择一件战利品</div>
            <div class="loot-options">
                <div class="loot-option" id="loot1">
                    <div class="loot-icon">?</div>
                    <div class="loot-name">未知物品</div>
                </div>
                <div class="loot-option" id="loot2">
                    <div class="loot-icon">?</div>
                    <div class="loot-name">未知物品</div>
                </div>
                <div class="loot-option" id="loot3">
                    <div class="loot-icon">?</div>
                    <div class="loot-name">未知物品</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 游戏模块 -->
    <script src="js/config/gameConfig.js"></script>
    <script src="js/core/gameState.js"></script>
    <script src="js/systems/map.js"></script>
    <script src="js/systems/audio.js"></script>
    <script src="js/systems/particle.js"></script>
    <script src="js/systems/item.js"></script>
    <script src="js/systems/chest.js"></script>
    <script src="js/systems/collision.js"></script>
    <script src="js/systems/door.js"></script>
    <script src="js/systems/tutorial.js"></script>
    <script src="js/entities/player.js"></script>
    <script src="js/entities/zombie.js"></script>
    <script src="js/entities/bullet.js"></script>
    <script src="js/levels/levelManager.js"></script>
    <script src="js/core/inputHandler.js"></script>
    <script src="js/systems/ui.js"></script>
    <script src="js/systems/render.js"></script>
    <script src="js/core/gameLoop.js"></script>
    <script src="js/main.js"></script>
</body>
</html>