// ====================
// 关卡管理系统模块
// ====================
(function() {
    // 初始化关卡
    function initLevel(level) {
        GameState.level = level;
        GameState.zombiesKilled = 0;
        GameState.zombiesToKill = CONFIG.LEVEL_ZOMBIE_COUNT[Math.min(level - 1, CONFIG.LEVEL_ZOMBIE_COUNT.length - 1)];
        
        // 清空对象
        GameState.zombies = [];
        GameState.bullets = [];
        GameState.particles = [];
        GameState.walls = [];
        GameState.doors = [];
        GameState.items = [];
        GameState.chests = []; // 清空宝箱
        
        // 生成平衡性优化的地图
        const mapData = MapSystem.generateBalancedMap();
        MapSystem.createWallsAndDoorsFromMap(mapData);
        
        // 创建光源（玩家手电筒）
        GameState.lights = [
            // 环境光（较暗）
            { x: 600, y: 400, radius: 300, intensity: 0.2 }
        ];
        
        // 高级关卡增加环境光源
        if (level >= 3) {
            GameState.lights.push({ x: 300, y: 200, radius: 150, intensity: 0.4 });
        }
        
        if (level >= 5) {
            GameState.lights.push({ x: 900, y: 600, radius: 150, intensity: 0.4 });
        }
        
        // 创建丧尸 - 确保不会在起始安全区内生成
        const zombieCount = GameState.zombiesToKill;
        for (let i = 0; i < zombieCount; i++) {
            // 根据关卡确定丧尸类型
            let type;
            const rand = Math.random();
            
            // 高级关卡出现更强的敌人
            if (level >= 6 && rand < 0.05) {
                type = 'BOOMER';
            } else if (level >= 5 && rand < 0.1) {
                type = 'TANK';
            } else if (rand < (0.2 + (level - 1) * 0.05)) {
                type = 'FAST';
            } else {
                type = 'NORMAL';
            }
            
            const zombieConfig = CONFIG.ZOMBIE_TYPES[type];
            
            // 在随机房间内生成丧尸，但避免在起始房间生成
            let zombieX, zombieY;
            let validPosition = false;
            let attempts = 0;
            
            while (!validPosition && attempts < 100) {
                // 随机选择一个房间（排除起始房间）
                let room;
                if (GameState.rooms.length > 1) {
                    let roomIndex;
                    do {
                        roomIndex = Math.floor(Math.random() * GameState.rooms.length);
                        room = GameState.rooms[roomIndex];
                    } while (room === GameState.startRoom && GameState.rooms.length > 1);
                } else {
                    room = GameState.rooms[0];
                }
                
                // 在房间内随机位置生成
                zombieX = (room.x + Math.random() * (room.width - 1)) * CONFIG.TILE_SIZE;
                zombieY = (room.y + Math.random() * (room.height - 1)) * CONFIG.TILE_SIZE;
                
                // 确保不生成在玩家附近（安全区）
                const distanceToPlayer = Math.sqrt(
                    Math.pow(zombieX - GameState.player.x, 2) +
                    Math.pow(zombieY - GameState.player.y, 2)
                );
                
                // 检查是否在起始房间内
                const isInStartRoom = GameState.startRoom && GameState.startRoom.containsPoint(
                    zombieX / CONFIG.TILE_SIZE, 
                    zombieY / CONFIG.TILE_SIZE
                );
                
                if (distanceToPlayer > CONFIG.SAFE_ZONE_BUFFER && !isInStartRoom) {
                    validPosition = true;
                }
                
                attempts++;
            }
            
            if (validPosition) {
                GameState.zombies.push({
                    x: zombieX,
                    y: zombieY,
                    width: 25,
                    height: 25,
                    health: zombieConfig.health,
                    maxHealth: zombieConfig.health,
                    angle: 0,
                    type: type,
                    speed: zombieConfig.speed
                });
            }
        }
        
        // 在随机房间中放置宝箱（根据关卡难度）
        const chestCount = Math.min(level, 3); // 最多3个宝箱
        for (let i = 0; i < chestCount; i++) {
            // 随机选择一个房间（排除起始房间）
            let room;
            if (GameState.rooms.length > 1) {
                let roomIndex;
                do {
                    roomIndex = Math.floor(Math.random() * GameState.rooms.length);
                    room = GameState.rooms[roomIndex];
                } while (room === GameState.startRoom && GameState.rooms.length > 1);
            } else {
                room = GameState.rooms[0];
            }
            
            // 在房间内随机位置生成宝箱
            const chestX = (room.x + Math.random() * (room.width - 1)) * CONFIG.TILE_SIZE;
            const chestY = (room.y + Math.random() * (room.height - 1)) * CONFIG.TILE_SIZE;
            
            // 确保不生成在玩家附近（安全区）
            const distanceToPlayer = Math.sqrt(
                Math.pow(chestX - GameState.player.x, 2) +
                Math.pow(chestY - GameState.player.y, 2)
            );
            
            // 检查是否在起始房间内
            const isInStartRoom = GameState.startRoom && GameState.startRoom.containsPoint(
                chestX / CONFIG.TILE_SIZE, 
                chestY / CONFIG.TILE_SIZE
            );
            
            if (distanceToPlayer > CONFIG.SAFE_ZONE_BUFFER && !isInStartRoom) {
                ChestSystem.spawnRandomChest(chestX, chestY);
            }
        }
        
        // 设置玩家位置（在第一个房间中心）
        if (GameState.startRoom) {
            GameState.player.x = (GameState.startRoom.x + GameState.startRoom.width / 2) * CONFIG.TILE_SIZE;
            GameState.player.y = (GameState.startRoom.y + GameState.startRoom.height / 2) * CONFIG.TILE_SIZE;
        } else {
            // 备用位置
            GameState.player.x = CONFIG.MAP_WIDTH * CONFIG.TILE_SIZE / 2;
            GameState.player.y = CONFIG.MAP_HEIGHT * CONFIG.TILE_SIZE / 2;
        }
        
        GameState.player.health = CONFIG.PLAYER_HEALTH;
        
        // 重置武器
        GameState.weapon.ammo = CONFIG.WEAPONS[GameState.weapon.type].ammo;
        GameState.weapon.isReloading = false;
        
        // 重置背包物品效果
        GameState.player.speedBoost = 0;
        
        // 更新UI
        UISystem.updateHealthUI();
        UISystem.updateAmmoUI();
        ItemSystem.updateInventoryUI();
    }

    // 检查关卡完成
    function checkLevelComplete() {
        // 确保在下一个事件循环中检查，避免在数组修改过程中检查
        setTimeout(() => {
            if (GameState.zombiesKilled >= GameState.zombiesToKill && GameState.zombies.length === 0) {
                // 显示战利品选择界面
                showLootSelection();
            }
        }, 0);
    }

    // 显示战利品选择
    function showLootSelection() {
        GameState.showingLoot = true;
        document.getElementById('lootSelection').classList.remove('hidden');
        
        // 生成三个随机战利品选项
        const lootOptions = [];
        for (let i = 0; i < 3; i++) {
            // 70%概率获得物品，30%概率获得武器
            if (Math.random() < 0.7) {
                // 随机物品
                const itemTypes = Object.keys(CONFIG.ITEMS);
                const randomType = itemTypes[Math.floor(Math.random() * itemTypes.length)];
                const itemConfig = CONFIG.ITEMS[randomType];
                
                lootOptions.push({
                    type: "item",
                    name: itemConfig.name,
                    icon: itemConfig.icon,
                    effect: itemConfig.effect,
                    value: itemConfig.value
                });
            } else {
                // 随机武器
                const weaponTypes = Object.keys(CONFIG.WEAPONS);
                const randomType = weaponTypes[Math.floor(Math.random() * weaponTypes.length)];
                const weaponConfig = CONFIG.WEAPONS[randomType];
                
                lootOptions.push({
                    type: "weapon",
                    name: weaponConfig.name,
                    icon: "🔫",
                    weaponType: randomType
                });
            }
        }
        
        // 更新UI
        for (let i = 0; i < 3; i++) {
            const option = document.getElementById(`loot${i+1}`);
            const loot = lootOptions[i];
            
            option.querySelector('.loot-icon').textContent = loot.icon;
            option.querySelector('.loot-name').textContent = loot.name;
            option.dataset.loot = JSON.stringify(loot);
        }
    }

    // 选择战利品
    function selectLoot(index) {
        const option = document.getElementById(`loot${index+1}`);
        const loot = JSON.parse(option.dataset.loot);
        
        if (loot.type === "weapon") {
            // 更换武器
            GameState.weapon.type = loot.weaponType;
            const weaponConfig = CONFIG.WEAPONS[loot.weaponType];
            GameState.weapon.ammo = weaponConfig.ammo;
            GameState.weapon.maxAmmo = weaponConfig.ammo;
            UISystem.updateAmmoUI();
        } else {
            // 添加到背包
            let emptySlot = -1;
            for (let i = 0; i < GameState.inventory.length; i++) {
                if (GameState.inventory[i] === null) {
                    emptySlot = i;
                    break;
                }
            }
            
            if (emptySlot !== -1) {
                GameState.inventory[emptySlot] = {
                    type: loot.type,
                    name: loot.name,
                    icon: loot.icon,
                    effect: loot.effect,
                    value: loot.value
                };
                ItemSystem.updateInventoryUI();
            }
        }
        
        // 隐藏战利品选择界面
        document.getElementById('lootSelection').classList.add('hidden');
        GameState.showingLoot = false;
        
        // 进入下一关
        setTimeout(() => {
            initLevel(GameState.level + 1);
        }, 1000);
    }

    // 导出关卡管理接口
    window.LevelManager = {
        initLevel: initLevel,
        checkLevelComplete: checkLevelComplete,
        showLootSelection: showLootSelection,
        selectLoot: selectLoot
    };
})();