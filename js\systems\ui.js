// ====================
// UI系统模块
// ====================
(function() {
    // 更新血条UI
    function updateHealthUI() {
        const healthPercent = (GameState.player.health / GameState.player.maxHealth) * 100;
        document.getElementById('healthFill').style.width = healthPercent + '%';
        document.getElementById('healthText').textContent = 
            Math.round(GameState.player.health) + '/' + GameState.player.maxHealth;
    }

    // 更新弹药UI
    function updateAmmoUI() {
        const weaponConfig = CONFIG.WEAPONS[GameState.weapon.type];
        document.getElementById('ammoCount').textContent = 
            GameState.weapon.ammo + '/' + weaponConfig.ammo + ' ' + weaponConfig.name;
    }

    // 检查玩家与丧尸碰撞
    function checkPlayerZombieCollision() {
        for (const zombie of GameState.zombies) {
            const dx = GameState.player.x - zombie.x;
            const dy = GameState.player.y - zombie.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            // 碰撞检测
            if (distance < 30) {
                GameState.player.health -= 0.5;
                AudioSystem.play('hurt');
                updateHealthUI();
                
                // 受伤粒子
                ParticleSystem.createParticles(GameState.player.x, GameState.player.y, 3, 'rgba(255, 0, 0, alpha)', 1);
                
                // 检查游戏结束
                if (GameState.player.health <= 0) {
                    // 显示游戏结束界面
                    document.getElementById('finalScore').textContent = '得分: ' + GameState.score;
                    document.getElementById('finalLevel').textContent = '关卡: ' + GameState.level;
                    
                    // 显示获得的战利品
                    let lootText = "获得战利品：";
                    let hasLoot = false;
                    for (let i = 0; i < GameState.inventory.length; i++) {
                        if (GameState.inventory[i]) {
                            if (!hasLoot) {
                                lootText += " ";
                                hasLoot = true;
                            }
                            lootText += GameState.inventory[i].name;
                            if (i < GameState.inventory.length - 1 && GameState.inventory[i+1]) {
                                lootText += ", ";
                            }
                        }
                    }
                    
                    if (!hasLoot) {
                        lootText += " 无";
                    }
                    
                    document.getElementById('lootInfo').textContent = lootText;
                    document.getElementById('gameOver').style.display = 'flex';
                }
            }
        }
    }

    // 导出UI系统接口
    window.UISystem = {
        updateHealthUI: updateHealthUI,
        updateAmmoUI: updateAmmoUI,
        checkPlayerZombieCollision: checkPlayerZombieCollision
    };
})();