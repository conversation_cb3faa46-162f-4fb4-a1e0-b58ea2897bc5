// ====================
// 游戏主循环模块
// ====================
(function() {
    // 游戏主循环
    function gameLoop() {
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        if (!GameState.isPaused && !GameState.showingLoot) {
            Player.update();
            Zombie.update();
            Bullet.update();
            ParticleSystem.updateParticles();
            UISystem.checkPlayerZombieCollision();
            TutorialSystem.check(); // 检查并显示教程
            GameState.gameTime++;
        }
        
        RenderSystem.render(ctx);
        requestAnimationFrame(gameLoop);
    }

    // 初始化游戏
    function initGame() {
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        AudioSystem.init();
        TutorialSystem.init(); // 初始化教程系统
        LevelManager.initLevel(1);
        InputHandler.setupEventListeners();
        gameLoop();
    }

    // 导出游戏主循环接口
    window.GameLoop = {
        init: initGame,
        loop: gameLoop
    };
})();