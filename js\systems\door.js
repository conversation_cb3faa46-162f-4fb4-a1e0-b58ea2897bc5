// ====================
// 门系统模块
// ====================
(function() {
    // 更新门的动画状态
    function updateDoors() {
        for (const door of GameState.doors) {
            // 更新开门/关门动画
            if (door.open && door.openProgress < 1.0) {
                door.openProgress = Math.min(1.0, door.openProgress + door.config.openSpeed);
            } else if (!door.open && door.openProgress > 0.0) {
                door.openProgress = Math.max(0.0, door.openProgress - door.config.openSpeed);
            }
        }
    }

    // 互动（开关门）
    function interactWithDoor() {
        // 找到最近的门
        let nearestDoor = null;
        let minDistance = Infinity;

        for (const door of GameState.doors) {
            const distance = Math.sqrt(
                Math.pow(door.centerX - GameState.player.x, 2) +
                Math.pow(door.centerY - GameState.player.y, 2)
            );

            if (distance < minDistance && distance < CONFIG.DOOR_INTERACT_DISTANCE) {
                minDistance = distance;
                nearestDoor = door;
            }
        }

        // 如果找到了门，开关它
        if (nearestDoor) {
            nearestDoor.open = !nearestDoor.open;
            AudioSystem.play('door');
        }
    }

    // 检查门是否阻挡移动（用于碰撞检测）
    function isDoorBlocking(door) {
        // 如果门完全打开，不阻挡移动
        if (door.openProgress >= 1.0) {
            return false;
        }

        // 如果门完全关闭或部分打开，都会阻挡移动
        return true;
    }

    // 获取门的实际碰撞区域（考虑开门进度）
    function getDoorCollisionRect(door) {
        if (!isDoorBlocking(door)) {
            return null; // 门完全打开，无碰撞
        }

        // 根据开门进度调整碰撞区域
        if (door.orientation === 'horizontal') {
            // 水平门：从两边向中间收缩
            const openWidth = door.width * door.openProgress;
            const remainingWidth = door.width - openWidth;

            return {
                x: door.x + openWidth / 2,
                y: door.y,
                width: remainingWidth,
                height: door.height
            };
        } else {
            // 垂直门：从上下向中间收缩
            const openHeight = door.height * door.openProgress;
            const remainingHeight = door.height - openHeight;

            return {
                x: door.x,
                y: door.y + openHeight / 2,
                width: door.width,
                height: remainingHeight
            };
        }
    }

    // 检查最近的可交互门
    function getNearestInteractableDoor() {
        let nearestDoor = null;
        let minDistance = Infinity;

        for (const door of GameState.doors) {
            const distance = Math.sqrt(
                Math.pow(door.centerX - GameState.player.x, 2) +
                Math.pow(door.centerY - GameState.player.y, 2)
            );

            if (distance < CONFIG.DOOR_INTERACT_DISTANCE && distance < minDistance) {
                minDistance = distance;
                nearestDoor = door;
            }
        }

        return nearestDoor;
    }

    // 导出门系统接口
    window.DoorSystem = {
        updateDoors: updateDoors,
        interactWithDoor: interactWithDoor,
        isDoorBlocking: isDoorBlocking,
        getDoorCollisionRect: getDoorCollisionRect,
        getNearestInteractableDoor: getNearestInteractableDoor
    };
})();