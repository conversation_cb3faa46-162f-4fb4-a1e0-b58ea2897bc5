// ====================
// 玩家模块
// ====================
(function() {
    // 更新玩家
    function updatePlayer() {
        // 保存当前位置
        const oldX = GameState.player.x;
        const oldY = GameState.player.y;
        
        // 计算移动速度
        const baseSpeed = GameState.player.isSprinting ? CONFIG.SPRINT_SPEED : CONFIG.PLAYER_SPEED;
        const speed = baseSpeed + GameState.player.speedBoost;
        
        // WASD移动
        if (GameState.keys['w']) GameState.player.y -= speed;
        if (GameState.keys['s']) GameState.player.y += speed;
        if (GameState.keys['a']) GameState.player.x -= speed;
        if (GameState.keys['d']) GameState.player.x += speed;
        
        // 检查与墙壁的碰撞
        if (CollisionSystem.checkWallCollision(GameState.player.x, GameState.player.y, GameState.player.width, GameState.player.height)) {
            // 恢复位置
            GameState.player.x = oldX;
            GameState.player.y = oldY;
        }
        
        // 确保玩家在边界内
        GameState.player.x = Math.max(15, Math.min(1185, GameState.player.x));
        GameState.player.y = Math.max(15, Math.min(785, GameState.player.y));
        
        // 疾跑检测
        GameState.player.isSprinting = GameState.keys['shift'] && 
                                      (GameState.keys['w'] || GameState.keys['a'] || 
                                       GameState.keys['s'] || GameState.keys['d']);
        
        // 脚步粒子
        if ((GameState.keys['w'] || GameState.keys['a'] || 
             GameState.keys['s'] || GameState.keys['d']) && 
            GameState.gameTime % 10 === 0) {
            const particleColor = GameState.player.isSprinting ? 
                'rgba(100, 100, 255, alpha)' : 'rgba(150, 150, 150, alpha)';
            ParticleSystem.createParticles(GameState.player.x, GameState.player.y + 15, 1, particleColor, 1);
        }
    }

    // 导出玩家模块接口
    window.Player = {
        update: updatePlayer
    };
})();