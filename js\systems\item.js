// ====================
// 物品系统模块
// ====================
(function() {
    // 生成随机物品
    function spawnItem(x, y) {
        const itemTypes = Object.keys(CONFIG.ITEMS);
        const randomType = itemTypes[Math.floor(Math.random() * itemTypes.length)];
        const itemConfig = CONFIG.ITEMS[randomType];
        
        GameState.items.push({
            x: x,
            y: y,
            type: randomType,
            name: itemConfig.name,
            icon: itemConfig.icon,
            effect: itemConfig.effect,
            value: itemConfig.value
        });
    }

    // 拾取物品
    function pickupItem(itemIndex) {
        const item = GameState.items[itemIndex];
        
        // 查找空的背包槽位
        let emptySlot = -1;
        for (let i = 0; i < GameState.inventory.length; i++) {
            if (GameState.inventory[i] === null) {
                emptySlot = i;
                break;
            }
        }
        
        // 如果有空槽位，拾取物品
        if (emptySlot !== -1) {
            GameState.inventory[emptySlot] = item;
            GameState.items.splice(itemIndex, 1);
            updateInventoryUI();
            return true;
        }
        
        return false; // 背包已满
    }

    // 使用物品
    function useItem(slotIndex) {
        const item = GameState.inventory[slotIndex];
        if (!item) return;
        
        switch (item.effect) {
            case "heal":
                GameState.player.health = Math.min(
                    GameState.player.maxHealth,
                    GameState.player.health + item.value
                );
                UISystem.updateHealthUI();
                break;
            case "ammo":
                GameState.weapon.ammo = Math.min(
                    GameState.weapon.maxAmmo,
                    GameState.weapon.ammo + item.value
                );
                UISystem.updateAmmoUI();
                break;
            case "speed":
                GameState.player.speedBoost = item.value;
                setTimeout(() => {
                    GameState.player.speedBoost = 0;
                }, 10000); // 10秒速度提升
                break;
            case "shield":
                // 简单实现：临时增加生命值上限
                GameState.player.maxHealth += item.value;
                GameState.player.health += item.value;
                UISystem.updateHealthUI();
                setTimeout(() => {
                    GameState.player.maxHealth -= item.value;
                    GameState.player.health = Math.min(
                        GameState.player.health,
                        GameState.player.maxHealth
                    );
                    UISystem.updateHealthUI();
                }, 15000); // 15秒护盾
                break;
        }
        
        // 移除已使用的物品
        GameState.inventory[slotIndex] = null;
        updateInventoryUI();
    }

    // 更新背包UI
    function updateInventoryUI() {
        for (let i = 0; i < 6; i++) {
            const slot = document.getElementById(`slot-${i}`);
            const item = GameState.inventory[i];
            
            if (item) {
                slot.innerHTML = item.icon;
                slot.title = item.name;
            } else {
                slot.innerHTML = "";
                slot.title = "空";
            }
            
            // 高亮当前选中物品
            if (i === GameState.activeItemSlot) {
                slot.classList.add("active");
            } else {
                slot.classList.remove("active");
            }
        }
    }

    // 拾取附近的物品
    function pickupNearbyItem() {
        for (let i = 0; i < GameState.items.length; i++) {
            const item = GameState.items[i];
            const distance = Math.sqrt(
                Math.pow(item.x - GameState.player.x, 2) +
                Math.pow(item.y - GameState.player.y, 2)
            );
            
            if (distance < CONFIG.ITEM_PICKUP_DISTANCE) {
                if (pickupItem(i)) {
                    AudioSystem.play('pickup');
                    return;
                } else {
                    // 背包已满
                    console.log("背包已满！");
                    return;
                }
            }
        }
    }

    // 导出物品系统接口
    window.ItemSystem = {
        spawnItem: spawnItem,
        pickupItem: pickupItem,
        useItem: useItem,
        updateInventoryUI: updateInventoryUI,
        pickupNearbyItem: pickupNearbyItem
    };
})();