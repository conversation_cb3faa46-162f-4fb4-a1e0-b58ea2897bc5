// ====================
// 摄像机系统模块
// ====================
(function() {
    // 摄像机状态
    const camera = {
        x: 600,                           // 摄像机世界坐标X
        y: 400,                           // 摄像机世界坐标Y
        targetX: 600,                     // 目标位置X
        targetY: 400,                     // 目标位置Y
        zoom: CONFIG.CAMERA.ZOOM_DEFAULT, // 当前缩放级别
        targetZoom: CONFIG.CAMERA.ZOOM_DEFAULT, // 目标缩放级别
        shakeX: 0,                        // 屏幕震动X
        shakeY: 0,                        // 屏幕震动Y
        shakeIntensity: 0,                // 震动强度
        shakeDecay: 0.9                   // 震动衰减
    };

    // 更新摄像机
    function update() {
        // 设置跟随目标（玩家）
        camera.targetX = GameState.player.x + CONFIG.CAMERA.OFFSET_X;
        camera.targetY = GameState.player.y + CONFIG.CAMERA.OFFSET_Y;

        // 平滑跟随
        const followSpeed = CONFIG.CAMERA.FOLLOW_SPEED;
        camera.x += (camera.targetX - camera.x) * followSpeed;
        camera.y += (camera.targetY - camera.y) * followSpeed;

        // 平滑缩放
        camera.zoom += (camera.targetZoom - camera.zoom) * CONFIG.CAMERA.ZOOM_SPEED;

        // 限制摄像机边界（防止超出地图）
        const mapPixelWidth = CONFIG.MAP_WIDTH * CONFIG.TILE_SIZE;
        const mapPixelHeight = CONFIG.MAP_HEIGHT * CONFIG.TILE_SIZE;
        const halfViewWidth = (CONFIG.CAMERA.VIEWPORT_WIDTH / 2) / camera.zoom;
        const halfViewHeight = (CONFIG.CAMERA.VIEWPORT_HEIGHT / 2) / camera.zoom;

        camera.x = Math.max(halfViewWidth, Math.min(mapPixelWidth - halfViewWidth, camera.x));
        camera.y = Math.max(halfViewHeight, Math.min(mapPixelHeight - halfViewHeight, camera.y));

        // 更新屏幕震动
        if (camera.shakeIntensity > 0) {
            camera.shakeX = (Math.random() - 0.5) * camera.shakeIntensity;
            camera.shakeY = (Math.random() - 0.5) * camera.shakeIntensity;
            camera.shakeIntensity *= camera.shakeDecay;
            
            if (camera.shakeIntensity < 0.1) {
                camera.shakeIntensity = 0;
                camera.shakeX = 0;
                camera.shakeY = 0;
            }
        }
    }

    // 世界坐标转屏幕坐标
    function worldToScreen(worldX, worldY) {
        const screenX = (worldX - camera.x) * camera.zoom + CONFIG.CAMERA.VIEWPORT_WIDTH / 2 + camera.shakeX;
        const screenY = (worldY - camera.y) * camera.zoom + CONFIG.CAMERA.VIEWPORT_HEIGHT / 2 + camera.shakeY;
        return { x: screenX, y: screenY };
    }

    // 屏幕坐标转世界坐标
    function screenToWorld(screenX, screenY) {
        const worldX = (screenX - CONFIG.CAMERA.VIEWPORT_WIDTH / 2 - camera.shakeX) / camera.zoom + camera.x;
        const worldY = (screenY - CONFIG.CAMERA.VIEWPORT_HEIGHT / 2 - camera.shakeY) / camera.zoom + camera.y;
        return { x: worldX, y: worldY };
    }

    // 设置摄像机变换矩阵
    function applyTransform(ctx) {
        ctx.save();
        
        // 移动到屏幕中心
        ctx.translate(CONFIG.CAMERA.VIEWPORT_WIDTH / 2, CONFIG.CAMERA.VIEWPORT_HEIGHT / 2);
        
        // 应用缩放
        ctx.scale(camera.zoom, camera.zoom);
        
        // 应用摄像机位置和震动
        ctx.translate(-camera.x + camera.shakeX / camera.zoom, -camera.y + camera.shakeY / camera.zoom);
    }

    // 恢复变换矩阵
    function resetTransform(ctx) {
        ctx.restore();
    }

    // 检查对象是否在视野内（用于优化渲染）
    function isInView(x, y, width, height) {
        const halfViewWidth = (CONFIG.CAMERA.VIEWPORT_WIDTH / 2) / camera.zoom;
        const halfViewHeight = (CONFIG.CAMERA.VIEWPORT_HEIGHT / 2) / camera.zoom;
        
        const left = camera.x - halfViewWidth;
        const right = camera.x + halfViewWidth;
        const top = camera.y - halfViewHeight;
        const bottom = camera.y + halfViewHeight;
        
        return !(x + width < left || x > right || y + height < top || y > bottom);
    }

    // 设置缩放级别
    function setZoom(zoom) {
        camera.targetZoom = Math.max(CONFIG.CAMERA.ZOOM_MIN, Math.min(CONFIG.CAMERA.ZOOM_MAX, zoom));
    }

    // 添加屏幕震动
    function addShake(intensity) {
        camera.shakeIntensity = Math.max(camera.shakeIntensity, intensity);
    }

    // 立即设置摄像机位置（无平滑）
    function setPosition(x, y) {
        camera.x = x;
        camera.y = y;
        camera.targetX = x;
        camera.targetY = y;
    }

    // 获取摄像机状态
    function getCamera() {
        return {
            x: camera.x,
            y: camera.y,
            zoom: camera.zoom,
            shakeX: camera.shakeX,
            shakeY: camera.shakeY
        };
    }

    // 自适应缩放（根据游戏情况）
    function adaptiveZoom() {
        // 根据僵尸数量和距离调整缩放
        let targetZoom = CONFIG.CAMERA.ZOOM_DEFAULT;
        
        if (GameState.zombies.length > 0) {
            // 计算最近僵尸的距离
            let minDistance = Infinity;
            for (const zombie of GameState.zombies) {
                const distance = Math.sqrt(
                    Math.pow(zombie.x - GameState.player.x, 2) + 
                    Math.pow(zombie.y - GameState.player.y, 2)
                );
                minDistance = Math.min(minDistance, distance);
            }
            
            // 如果僵尸很近，稍微缩小视野以增加紧张感
            if (minDistance < 150) {
                targetZoom = CONFIG.CAMERA.ZOOM_DEFAULT * 1.1;
            }
            // 如果僵尸较远，扩大视野以便观察
            else if (minDistance > 300) {
                targetZoom = CONFIG.CAMERA.ZOOM_DEFAULT * 0.9;
            }
        }
        
        setZoom(targetZoom);
    }

    // 导出摄像机系统接口
    window.CameraSystem = {
        update: update,
        worldToScreen: worldToScreen,
        screenToWorld: screenToWorld,
        applyTransform: applyTransform,
        resetTransform: resetTransform,
        isInView: isInView,
        setZoom: setZoom,
        addShake: addShake,
        setPosition: setPosition,
        getCamera: getCamera,
        adaptiveZoom: adaptiveZoom
    };
})();
