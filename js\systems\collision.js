// ====================
// 碰撞检测系统模块
// ====================
(function() {
    // 检查矩形碰撞
    function checkRectCollision(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }

    // 检查点是否在矩形内
    function isPointInRect(px, py, rect) {
        return px >= rect.x && px <= rect.x + rect.width &&
               py >= rect.y && py <= rect.y + rect.height;
    }

    // 检查与墙壁的碰撞
    function checkWallCollision(x, y, width, height) {
        const rect = { x: x - width/2, y: y - height/2, width: width, height: height };
        
        // 检查与墙壁的碰撞
        for (const wall of GameState.walls) {
            if (checkRectCollision(rect, wall)) {
                return true;
            }
        }
        
        // 检查与门的碰撞（使用新的门系统）
        for (const door of GameState.doors) {
            const doorCollisionRect = DoorSystem.getDoorCollisionRect(door);
            if (doorCollisionRect && checkRectCollision(rect, doorCollisionRect)) {
                return true;
            }
        }
        
        return false;
    }

    // 导出碰撞检测系统接口
    window.CollisionSystem = {
        checkRectCollision: checkRectCollision,
        isPointInRect: isPointInRect,
        checkWallCollision: checkWallCollision
    };
})();